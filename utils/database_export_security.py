"""
Database Export Security Module
Provides comprehensive security measures, audit logging, and access control
for database export operations.
"""

import os
import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import ipaddress

from utils.monitoring_system import record_security_event, emit_alert, AlertLevel
from database.operations import get_log_settings, is_owner
from handlers.sys_db import is_privileged

logger = logging.getLogger(__name__)

# Security configuration
SECURITY_LOG_PATH = Path("__pycache__/db_export_security.log")
AUDIT_LOG_PATH = Path("__pycache__/db_export_audit.json")
MAX_EXPORT_ATTEMPTS_PER_HOUR = 10  # Increased for legitimate use
MAX_FAILED_ATTEMPTS_PER_DAY = 50   # Much higher for testing and legitimate use
SUSPICIOUS_ACTIVITY_THRESHOLD = 3

class DatabaseExportSecurity:
    """Handles security measures for database export operations."""
    
    def __init__(self):
        """Initialize security module."""
        self.failed_attempts = {}  # user_id -> [timestamps]
        self.export_attempts = {}  # user_id -> [timestamps]
        self.suspicious_activities = {}  # user_id -> count
        self.audit_log = []
        self._load_audit_log()
        self._setup_security_logging()
    
    def _setup_security_logging(self):
        """Setup dedicated security logging for database exports."""
        try:
            # Ensure security log directory exists
            SECURITY_LOG_PATH.parent.mkdir(parents=True, exist_ok=True)
            
            # Setup security logger
            security_logger = logging.getLogger("db_export_security")
            security_logger.setLevel(logging.INFO)
            
            # Create file handler if not exists
            if not security_logger.handlers:
                handler = logging.FileHandler(SECURITY_LOG_PATH)
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                handler.setFormatter(formatter)
                security_logger.addHandler(handler)
            
        except Exception as e:
            logger.error(f"Error setting up security logging: {e}")
    
    def _load_audit_log(self):
        """Load existing audit log from file."""
        try:
            if AUDIT_LOG_PATH.exists():
                with open(AUDIT_LOG_PATH, 'r') as f:
                    self.audit_log = json.load(f)
                    
                # Keep only last 1000 entries
                if len(self.audit_log) > 1000:
                    self.audit_log = self.audit_log[-1000:]
                    self._save_audit_log()
                    
        except Exception as e:
            logger.error(f"Error loading audit log: {e}")
            self.audit_log = []
    
    def _save_audit_log(self):
        """Save audit log to file."""
        try:
            AUDIT_LOG_PATH.parent.mkdir(parents=True, exist_ok=True)
            with open(AUDIT_LOG_PATH, 'w') as f:
                json.dump(self.audit_log, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving audit log: {e}")
    
    def validate_export_request(self, user_id: int, username: str, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a database export request for security compliance.
        
        Args:
            user_id: User ID making the request
            username: Username making the request
            request_data: Export request parameters
            
        Returns:
            Dict with validation results
        """
        validation_result = {
            "allowed": False,
            "reason": None,
            "security_level": "normal",
            "restrictions": []
        }
        
        try:
            # Check if user has proper privileges (same pattern as admin_diagnostics.py)
            if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
                validation_result.update({
                    "allowed": False,
                    "reason": "Insufficient privileges - Owner access required",
                    "security_level": "critical"
                })
                
                self._log_security_event("unauthorized_export_attempt", {
                    "user_id": user_id,
                    "username": username,
                    "reason": "insufficient_privileges"
                })
                
                return validation_result
            
            # Check rate limiting
            rate_limit_result = self._check_rate_limits(user_id)
            if not rate_limit_result["allowed"]:
                validation_result.update({
                    "allowed": False,
                    "reason": rate_limit_result["reason"],
                    "security_level": "high"
                })
                
                self._log_security_event("rate_limit_exceeded", {
                    "user_id": user_id,
                    "username": username,
                    "limit_type": rate_limit_result["limit_type"]
                })
                
                return validation_result
            
            # Check for suspicious patterns
            if self._detect_suspicious_activity(user_id, request_data):
                validation_result.update({
                    "allowed": False,
                    "reason": "Suspicious activity detected",
                    "security_level": "critical"
                })
                
                self._log_security_event("suspicious_export_activity", {
                    "user_id": user_id,
                    "username": username,
                    "request_data": request_data
                })
                
                return validation_result
            
            # Validate export parameters
            param_validation = self._validate_export_parameters(request_data)
            if not param_validation["valid"]:
                validation_result.update({
                    "allowed": False,
                    "reason": f"Invalid parameters: {param_validation['reason']}",
                    "security_level": "medium"
                })
                
                return validation_result
            
            # All checks passed
            validation_result.update({
                "allowed": True,
                "reason": "Request validated successfully",
                "security_level": "normal"
            })
            
            # Log successful validation
            self._log_security_event("export_request_validated", {
                "user_id": user_id,
                "username": username,
                "request_data": request_data
            })
            
            # Record export attempt
            self._record_export_attempt(user_id)
            
        except Exception as e:
            logger.error(f"Error in export request validation: {e}")
            validation_result.update({
                "allowed": False,
                "reason": f"Validation error: {str(e)}",
                "security_level": "critical"
            })
            
            self._log_security_event("export_validation_error", {
                "user_id": user_id,
                "username": username,
                "error": str(e)
            })
        
        return validation_result
    
    def _check_rate_limits(self, user_id: int) -> Dict[str, Any]:
        """Check if user has exceeded rate limits."""
        current_time = datetime.now()
        hour_ago = current_time - timedelta(hours=1)
        day_ago = current_time - timedelta(days=1)
        
        # Clean old attempts
        if user_id in self.export_attempts:
            self.export_attempts[user_id] = [
                timestamp for timestamp in self.export_attempts[user_id]
                if timestamp > day_ago
            ]
        
        if user_id in self.failed_attempts:
            self.failed_attempts[user_id] = [
                timestamp for timestamp in self.failed_attempts[user_id]
                if timestamp > day_ago
            ]
        
        # Check hourly export limit
        recent_attempts = self.export_attempts.get(user_id, [])
        recent_attempts_count = len([t for t in recent_attempts if t > hour_ago])
        
        if recent_attempts_count >= MAX_EXPORT_ATTEMPTS_PER_HOUR:
            return {
                "allowed": False,
                "reason": f"Too many export attempts ({recent_attempts_count}/{MAX_EXPORT_ATTEMPTS_PER_HOUR} per hour)",
                "limit_type": "hourly_exports"
            }
        
        # Check daily failed attempts limit (more lenient for owners)
        recent_failures = self.failed_attempts.get(user_id, [])
        recent_failures_count = len([t for t in recent_failures if t > day_ago])

        # Higher limit for owners
        max_failures = MAX_FAILED_ATTEMPTS_PER_DAY * 2 if is_owner(user_id) else MAX_FAILED_ATTEMPTS_PER_DAY

        if recent_failures_count >= max_failures:
            return {
                "allowed": False,
                "reason": f"Too many failed attempts ({recent_failures_count}/{max_failures} per day)",
                "limit_type": "daily_failures"
            }
        
        return {"allowed": True}
    
    def _detect_suspicious_activity(self, user_id: int, request_data: Dict[str, Any]) -> bool:
        """Detect suspicious patterns in export requests."""
        try:
            # Skip suspicious activity detection for owners - they have full access
            if is_owner(user_id):
                return False

            suspicious_indicators = 0

            # Check for rapid successive requests (more lenient for legitimate users)
            if user_id in self.export_attempts:
                recent_attempts = [
                    t for t in self.export_attempts[user_id]
                    if t > datetime.now() - timedelta(minutes=5)
                ]
                # Increased threshold for rapid requests
                if len(recent_attempts) > 5:
                    suspicious_indicators += 1

            # Check for unusual export parameters (only for non-owners)
            if request_data.get("collections"):
                collections = request_data["collections"]
                # Suspicious if requesting sensitive collections only AND user is not owner
                sensitive_collections = ["admins", "settings", "payments"]
                if all(col in sensitive_collections for col in collections) and not is_owner(user_id):
                    suspicious_indicators += 1

            # Remove off-hours check as it's too restrictive for legitimate use

            # Update suspicious activity counter (higher threshold)
            if suspicious_indicators > 0:
                self.suspicious_activities[user_id] = self.suspicious_activities.get(user_id, 0) + 1

                # Increased threshold for suspicious activity
                if self.suspicious_activities[user_id] >= (SUSPICIOUS_ACTIVITY_THRESHOLD * 2):
                    return True

            return False
            
        except Exception as e:
            logger.error(f"Error in suspicious activity detection: {e}")
            return False
    
    def _validate_export_parameters(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate export request parameters."""
        try:
            # Check export format
            valid_formats = ["bson_archive", "bson_compressed", "json", "json_compressed"]
            export_format = request_data.get("format", "bson_archive")
            
            if export_format not in valid_formats:
                return {
                    "valid": False,
                    "reason": f"Invalid export format: {export_format}"
                }
            
            # Check collections if specified
            if "collections" in request_data:
                collections = request_data["collections"]
                # Allow None (means export all collections)
                if collections is not None and not isinstance(collections, list):
                    return {
                        "valid": False,
                        "reason": "Collections must be a list"
                    }
                
                # Check for valid collection names (basic validation) - only if collections is not None
                if collections is not None:
                    for collection in collections:
                        if not isinstance(collection, str) or not collection.strip():
                            return {
                                "valid": False,
                                "reason": f"Invalid collection name: {collection}"
                            }
            
            # Check date filter if specified
            if "date_filter" in request_data:
                date_filter = request_data["date_filter"]
                # Allow None (means no date filtering)
                if date_filter is not None and not isinstance(date_filter, dict):
                    return {
                        "valid": False,
                        "reason": "Date filter must be a dictionary"
                    }
            
            return {"valid": True}
            
        except Exception as e:
            return {
                "valid": False,
                "reason": f"Parameter validation error: {str(e)}"
            }
    
    def _record_export_attempt(self, user_id: int):
        """Record an export attempt for rate limiting."""
        if user_id not in self.export_attempts:
            self.export_attempts[user_id] = []
        
        self.export_attempts[user_id].append(datetime.now())
    
    def record_export_failure(self, user_id: int, username: str, error: str):
        """Record a failed export attempt."""
        if user_id not in self.failed_attempts:
            self.failed_attempts[user_id] = []
        
        self.failed_attempts[user_id].append(datetime.now())
        
        self._log_security_event("export_failure", {
            "user_id": user_id,
            "username": username,
            "error": error
        })
    
    def record_export_success(self, user_id: int, username: str, export_info: Dict[str, Any]):
        """Record a successful export."""
        self._log_security_event("export_success", {
            "user_id": user_id,
            "username": username,
            "export_id": export_info.get("export_id"),
            "format": export_info.get("format"),
            "file_size": export_info.get("file_size"),
            "collections": export_info.get("collections")
        })
    
    def _log_security_event(self, event_type: str, event_data: Dict[str, Any]):
        """Log a security event."""
        try:
            # Create audit entry
            audit_entry = {
                "timestamp": datetime.now().isoformat(),
                "event_type": event_type,
                "data": event_data,
                "severity": self._get_event_severity(event_type)
            }
            
            # Add to audit log
            self.audit_log.append(audit_entry)
            
            # Keep only last 1000 entries
            if len(self.audit_log) > 1000:
                self.audit_log = self.audit_log[-1000:]
            
            # Save audit log
            self._save_audit_log()
            
            # Log to security logger
            security_logger = logging.getLogger("db_export_security")
            security_logger.info(f"{event_type}: {json.dumps(event_data)}")
            
            # Record in monitoring system
            record_security_event(f"db_export_{event_type}", event_data)
            
            # Emit alert for critical events
            if audit_entry["severity"] == "critical":
                emit_alert(
                    AlertLevel.CRITICAL,
                    f"Critical database export security event: {event_type}",
                    event_data
                )
            elif audit_entry["severity"] == "high":
                emit_alert(
                    AlertLevel.WARNING,
                    f"High priority database export security event: {event_type}",
                    event_data
                )
            
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
    
    def _get_event_severity(self, event_type: str) -> str:
        """Get severity level for an event type."""
        critical_events = [
            "unauthorized_export_attempt",
            "suspicious_export_activity",
            "export_validation_error"
        ]
        
        high_events = [
            "rate_limit_exceeded",
            "export_failure"
        ]
        
        if event_type in critical_events:
            return "critical"
        elif event_type in high_events:
            return "high"
        else:
            return "normal"
    
    def get_security_statistics(self) -> Dict[str, Any]:
        """Get security statistics for database exports."""
        try:
            current_time = datetime.now()
            day_ago = current_time - timedelta(days=1)
            week_ago = current_time - timedelta(days=7)
            
            # Filter recent audit entries
            recent_entries = [
                entry for entry in self.audit_log
                if datetime.fromisoformat(entry["timestamp"]) > week_ago
            ]
            
            # Count events by type and severity
            event_counts = {}
            severity_counts = {"critical": 0, "high": 0, "normal": 0}
            
            for entry in recent_entries:
                event_type = entry["event_type"]
                severity = entry["severity"]
                
                event_counts[event_type] = event_counts.get(event_type, 0) + 1
                severity_counts[severity] += 1
            
            return {
                "total_security_events": len(recent_entries),
                "events_by_type": event_counts,
                "events_by_severity": severity_counts,
                "active_rate_limits": len(self.export_attempts),
                "users_with_failed_attempts": len(self.failed_attempts),
                "suspicious_users": len(self.suspicious_activities),
                "audit_log_size": len(self.audit_log),
                "report_time": current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating security statistics: {e}")
            return {"error": str(e)}

    def reset_user_security_counters(self, user_id: int):
        """Reset security counters for a specific user (for testing/admin purposes)."""
        try:
            if user_id in self.export_attempts:
                del self.export_attempts[user_id]
            if user_id in self.failed_attempts:
                del self.failed_attempts[user_id]
            if user_id in self.suspicious_activities:
                del self.suspicious_activities[user_id]

            logger.info(f"Reset security counters for user {user_id}")

        except Exception as e:
            logger.error(f"Error resetting security counters for user {user_id}: {e}")

    def get_user_security_info(self, user_id: int) -> Dict[str, Any]:
        """Get security information for a specific user."""
        try:
            current_time = datetime.now()
            day_ago = current_time - timedelta(days=1)
            
            # Get user's recent activities
            user_entries = [
                entry for entry in self.audit_log
                if entry["data"].get("user_id") == user_id and
                datetime.fromisoformat(entry["timestamp"]) > day_ago
            ]
            
            return {
                "user_id": user_id,
                "recent_export_attempts": len(self.export_attempts.get(user_id, [])),
                "recent_failed_attempts": len(self.failed_attempts.get(user_id, [])),
                "suspicious_activity_count": self.suspicious_activities.get(user_id, 0),
                "recent_security_events": len(user_entries),
                "last_activity": max(
                    [datetime.fromisoformat(entry["timestamp"]) for entry in user_entries],
                    default=None
                ),
                "security_level": self._assess_user_security_level(user_id)
            }
            
        except Exception as e:
            logger.error(f"Error getting user security info: {e}")
            return {"error": str(e)}
    
    def _assess_user_security_level(self, user_id: int) -> str:
        """Assess security risk level for a user."""
        try:
            failed_attempts = len(self.failed_attempts.get(user_id, []))
            suspicious_count = self.suspicious_activities.get(user_id, 0)
            
            if suspicious_count >= SUSPICIOUS_ACTIVITY_THRESHOLD or failed_attempts >= MAX_FAILED_ATTEMPTS_PER_DAY:
                return "high_risk"
            elif failed_attempts > 3 or suspicious_count > 1:
                return "medium_risk"
            else:
                return "low_risk"
                
        except Exception:
            return "unknown"

# Global security instance
export_security = DatabaseExportSecurity()

# Convenience functions
def validate_export_request(user_id: int, username: str, request_data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate a database export request."""
    return export_security.validate_export_request(user_id, username, request_data)

def record_export_failure(user_id: int, username: str, error: str):
    """Record a failed export attempt."""
    export_security.record_export_failure(user_id, username, error)

def record_export_success(user_id: int, username: str, export_info: Dict[str, Any]):
    """Record a successful export."""
    export_security.record_export_success(user_id, username, export_info)

def get_security_statistics() -> Dict[str, Any]:
    """Get security statistics."""
    return export_security.get_security_statistics()

def get_user_security_info(user_id: int) -> Dict[str, Any]:
    """Get user security information."""
    return export_security.get_user_security_info(user_id)

def reset_user_security_counters(user_id: int):
    """Reset security counters for a user (for testing/admin purposes)."""
    export_security.reset_user_security_counters(user_id)
