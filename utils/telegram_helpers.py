"""
Helper utilities for Telegram API calls with error handling and rate limiting.

This module provides utilities for safely sending and editing Telegram messages
with proper error handling, rate limiting, HTML sanitization, and reply markup
size validation.

Key features:
- Automatic keyboard size validation and truncation
- Handles "reply markup is too long" errors gracefully
- Provides fallback mechanisms for oversized keyboards
- Maintains user experience by truncating rather than failing
"""

import logging
import re
import traceback
import asyncio
import threading
import concurrent.futures  # Added missing import
from typing import Optional, Union, Dict, Any, List, Tuple
from aiogram import Bot
from aiogram.types import (
    Message,
    InlineKeyboardMarkup,
    ReplyKeyboardMarkup,
    FSInputFile,
    BufferedInputFile,
    InputMediaPhoto,
    # Import specific button types for type checking if needed, e.g.:
    # InlineKeyboardButton, KeyboardButton
)
from utils.helpers import resolve_image_path

# Removed asyncio.Future import as it wasn't explicitly used
from aiogram.exceptions import TelegramAPIError, TelegramBadRequest, TelegramRetryAfter

# Import rate limiters (assuming they exist in utils.rate_limiter)
try:
    from utils.rate_limiter import rate_limit_message_to_chat, rate_limit_media
except ImportError:
    # Provide dummy decorators if rate limiters are not found
    logger = logging.getLogger(__name__)
    logger.warning("Rate limiter module not found. Using dummy decorators.")

    def rate_limit_message_to_chat(rps=None):
        def decorator(func):
            return func

        return decorator

    def rate_limit_media(rps=None):
        def decorator(func):
            return func

        return decorator


logger = logging.getLogger(__name__)

# Thread-local storage for event loops
thread_local = threading.local()

# Maximum message length for Telegram
MAX_MESSAGE_LENGTH = 4096
# Maximum caption length for media messages (photos, videos, etc.)
MAX_CAPTION_LENGTH = 1024
# Maximum reply markup size (approximate limit based on Telegram's restrictions)
MAX_REPLY_MARKUP_SIZE = 8192  # Conservative estimate in bytes
MAX_KEYBOARD_BUTTONS = 100  # Maximum number of buttons before truncation

# --- Event Loop Management ---


def get_event_loop() -> asyncio.AbstractEventLoop:
    """
    Get or create an event loop for the current thread.

    Ensures each thread interacting with asyncio has its own loop if it's not the main thread.

    Returns:
        asyncio.AbstractEventLoop: The event loop for the current thread.
    """
    if threading.current_thread() is threading.main_thread():
        try:
            return asyncio.get_running_loop()
        except RuntimeError:
            # Main thread might not have a running loop initially
            try:
                # Check if a loop exists but isn't running
                loop = asyncio.get_event_loop_policy().get_event_loop()
                # If it's closed, create a new one
                if loop.is_closed():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                return loop
            except RuntimeError:
                # No loop exists, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return loop
    else:
        # For non-main threads, use thread-local storage
        if not hasattr(thread_local, "loop") or thread_local.loop.is_closed():
            logger.debug(
                f"Creating new event loop for thread {threading.current_thread().name}"
            )
            thread_local.loop = asyncio.new_event_loop()
            # No need to call set_event_loop here for non-main threads,
            # as run_coroutine_threadsafe will target this specific loop.
        return thread_local.loop


def run_async(coroutine):
    """
    Run a coroutine safely from a synchronous context or a different thread.

    Ensures the coroutine runs within an asyncio Task for proper context management
    (e.g., for timeout contexts used within the coroutine).

    Args:
        coroutine: The coroutine object to run.

    Returns:
        The result of the coroutine, or None if execution times out or fails critically.
    """
    loop = get_event_loop()

    # Wrapper to ensure the coroutine runs as a task
    async def task_wrapper():
        # Create task ensures context for things like asyncio.timeout inside the coroutine
        task = asyncio.create_task(coroutine)
        try:
            # Await the task without adding another layer of timeout here.
            # Let timeouts be handled within the coroutine or by future.result().
            return await task
        except asyncio.CancelledError:
            logger.warning(
                f"Task {task.get_name()} was cancelled during execution in run_async."
            )
            raise  # Re-raise cancellation
        except Exception as e:
            logger.error(
                f"Exception caught within task_wrapper for {getattr(coroutine, '__name__', 'coroutine')}: {e}",
                exc_info=True,
            )
            raise  # Re-raise exception to be caught by future.result() or run_until_complete()

    if loop.is_running():
        # If called from an async context or thread with a running loop
        logger.debug(
            f"Scheduling coroutine on running loop {id(loop)} from thread {threading.current_thread().name}"
        )

        # Instead of using run_coroutine_threadsafe, create and schedule task directly
        # when possible to ensure proper task context
        if threading.current_thread() is threading.main_thread():
            try:
                # Create a task directly on the running loop
                return asyncio.create_task(task_wrapper())
            except RuntimeError as e:
                logger.error(f"Failed to create task in running loop: {e}")
                # Fall back to run_coroutine_threadsafe

        # Use run_coroutine_threadsafe for cross-thread execution
        future = asyncio.run_coroutine_threadsafe(task_wrapper(), loop)
        try:
            # Wait for the result with a timeout to prevent indefinite blocking
            # Consider making this timeout configurable if 30s is not always appropriate
            return future.result(timeout=30)
        except concurrent.futures.TimeoutError:
            logger.error(
                f"run_async: Timeout waiting for coroutine result from thread {threading.current_thread().name}."
            )
            future.cancel()  # Attempt to cancel the underlying task
            return None
        except asyncio.CancelledError:
            logger.warning(
                f"run_async: Future was cancelled for coroutine from thread {threading.current_thread().name}."
            )
            return None
        except Exception as e:
            # This catches exceptions *raised* by the coroutine via the future
            logger.error(
                f"run_async: Coroutine raised an exception: {e}", exc_info=False
            )  # exc_info=False as task_wrapper logged it
            # Optionally re-raise or return None based on desired behavior
            return None  # Returning None for robustness
    else:
        # If called from a sync context where the loop isn't running
        logger.debug(
            f"Running coroutine to completion on loop {id(loop)} from thread {threading.current_thread().name}"
        )
        try:
            # Use run_until_complete which handles task creation implicitly
            return loop.run_until_complete(task_wrapper())
        except Exception as e:
            logger.error(
                f"run_async: Exception during run_until_complete: {e}", exc_info=True
            )
            return None


# --- HTML Sanitization ---


def sanitize_html(text: str) -> str:
    """
    Sanitizes HTML text for Telegram, keeping supported tags and ensuring proper structure.

    Supported tags: <b>, <i>, <u>, <s>, <tg-spoiler>, <a>, <code>, <pre>, <blockquote>
    Note: <tg-spoiler> is supported in newer clients. Added it for completeness.

    Args:
        text: The input HTML string.

    Returns:
        Sanitized HTML string.
    """
    if not text or not isinstance(text, str):
        return ""

    # 1. Escape naked '<', '>' '&' characters to avoid them being confused with tags/entities
    text = text.replace("&", "&")  # Must be first
    text = re.sub(r"<(?![a-zA-Z/])", "<", text)  # < not followed by letter or /
    text = re.sub(
        r"(?<![a-zA-Z0-9/\"\' ])>", ">", text
    )  # > not preceded by letter, digit, /, ", ', space

    # 2. Define supported tags and attributes (<a> needs href)
    supported_tags = {
        "b",
        "strong",
        "i",
        "em",
        "u",
        "ins",
        "s",
        "strike",
        "del",
        "tg-spoiler",
        "a",
        "code",
        "pre",
        "blockquote",
    }
    # Mapping alternative tags to preferred Telegram tags
    tag_map = {"strong": "b", "em": "i", "ins": "u", "strike": "s", "del": "s"}

    # 3. Regex to find all tags
    tag_pattern = re.compile(r"<(/?)([a-zA-Z\-_]+)([^>]*)>")

    # 4. Process tags using a stack
    sanitized_parts = []
    open_tags = []  # Stack to track open tags (stores the preferred tag name)
    last_pos = 0

    for match in tag_pattern.finditer(text):
        # Add text segment before the current tag
        sanitized_parts.append(text[last_pos : match.start()])

        is_closing_tag = match.group(1) == "/"
        tag_name_raw = match.group(2).lower()
        attributes_str = match.group(3).strip()

        # Map to preferred tag name
        tag_name = tag_map.get(tag_name_raw, tag_name_raw)

        valid_tag = False
        if tag_name in supported_tags:
            if is_closing_tag:
                # Closing tag: Check if it matches the last open tag
                if open_tags and open_tags[-1] == tag_name:
                    open_tags.pop()
                    sanitized_parts.append(f"</{tag_name}>")
                    valid_tag = True
                # Else: ignore mismatched/unexpected closing tags
            else:
                # Opening tag: Check validity
                if tag_name == "a":
                    href_match = re.search(
                        r'href\s*=\s*("|\')([^"\']+)\1', attributes_str, re.IGNORECASE
                    )
                    if href_match:
                        # Keep only href, escape quotes in URL
                        href = (
                            href_match.group(2).replace('"', "%22").replace("'", "%27")
                        )
                        sanitized_parts.append(f'<a href="{href}">')
                        open_tags.append(tag_name)
                        valid_tag = True
                    # Else: Invalid <a> tag without href, ignore
                elif tag_name == "pre":
                    # Check for <pre><code class="language-..."> structure
                    lang_match = re.search(
                        r'class\s*=\s*("|\')language-([a-zA-Z0-9\-_]+)\1',
                        attributes_str,
                        re.IGNORECASE,
                    )
                    if lang_match:
                        lang = lang_match.group(2)
                        # Check if the *next* tag is <code>
                        next_code_match = re.match(
                            r"\s*<code>", text[match.end() :], re.IGNORECASE
                        )
                        if next_code_match:
                            # Output <pre><code class="language-..."> structure
                            sanitized_parts.append(
                                f'<pre><code class="language-{lang}">'
                            )
                            # Add both 'pre' and 'code' to stack; assumes </code></pre> closing
                            open_tags.append("pre")  # Need to close pre later
                            open_tags.append("code")  # Need to close code first
                            valid_tag = True  # Handled 'pre' and opening 'code'
                        else:
                            # <pre> without immediate <code>, treat as standard pre
                            sanitized_parts.append("<pre>")
                            open_tags.append(tag_name)
                            valid_tag = True
                    else:
                        # Standard <pre> tag
                        sanitized_parts.append("<pre>")
                        open_tags.append(tag_name)
                        valid_tag = True
                elif tag_name == "code":
                    # Need to ensure this isn't inside a <pre> that already handled it
                    if not (
                        open_tags
                        and open_tags[-1] == "code"
                        and len(open_tags) > 1
                        and open_tags[-2] == "pre"
                    ):
                        sanitized_parts.append("<code>")
                        open_tags.append(tag_name)
                        valid_tag = True
                else:
                    # Other supported opening tags
                    sanitized_parts.append(f"<{tag_name}>")
                    open_tags.append(tag_name)
                    valid_tag = True

        if not valid_tag:
            # If the tag was invalid or ignored, append its original text representation
            # This preserves content like `<invalid>` as text "<invalid>"
            sanitized_parts.append(match.group(0).replace("<", "<").replace(">", ">"))

        last_pos = match.end()

    # Append remaining text after the last tag
    sanitized_parts.append(text[last_pos:])

    # Close any remaining open tags in reverse order
    while open_tags:
        tag = open_tags.pop()
        sanitized_parts.append(f"</{tag}>")

    # Join parts and perform one final check for safety: remove any remaining < or >
    # This handles potential errors in the logic above or tricky inputs.
    final_text = "".join(sanitized_parts)
    # final_text = final_text.replace("<", "<").replace(">", ">") # Too aggressive? Re-evaluate.
    # Let's trust the process above for supported tags, but maybe escape only if it looks broken.
    # A minimal final check: if "<" appears without a following supported tag char or "/", escape it.
    final_text = re.sub(r"<(?![/a-zA-Z])", "<", final_text)

    return final_text


# --- Synchronous HTTP Sender (Fallback / Specific Use Cases) ---


def _estimate_markup_size(markup: Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]) -> int:
    """Estimate the size of a reply markup in bytes."""
    if not markup:
        return 0

    try:
        import json
        markup_dict = _serialize_markup_to_dict(markup)
        if markup_dict:
            return len(json.dumps(markup_dict, ensure_ascii=False).encode('utf-8'))
        return 0
    except Exception as e:
        logger.warning(f"Error estimating markup size: {e}")
        return 0


def _count_keyboard_buttons(markup: Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]) -> int:
    """Count the total number of buttons in a keyboard."""
    if not markup:
        return 0

    try:
        if isinstance(markup, InlineKeyboardMarkup):
            return sum(len(row) for row in markup.inline_keyboard)
        elif isinstance(markup, ReplyKeyboardMarkup):
            return sum(len(row) for row in markup.keyboard)
        return 0
    except Exception as e:
        logger.warning(f"Error counting keyboard buttons: {e}")
        return 0


def _truncate_keyboard(markup: Union[InlineKeyboardMarkup, ReplyKeyboardMarkup], max_buttons: int = MAX_KEYBOARD_BUTTONS) -> Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]:
    """Truncate a keyboard to fit within size limits."""
    if not markup:
        return markup

    try:
        if isinstance(markup, InlineKeyboardMarkup):
            truncated_rows = []
            button_count = 0

            for row in markup.inline_keyboard:
                if button_count + len(row) <= max_buttons:
                    truncated_rows.append(row)
                    button_count += len(row)
                else:
                    # Add as many buttons from this row as possible
                    remaining_slots = max_buttons - button_count
                    if remaining_slots > 0:
                        truncated_rows.append(row[:remaining_slots])
                    break

            # Add a "More..." button if we truncated
            if button_count >= max_buttons and truncated_rows:
                from aiogram.types import InlineKeyboardButton
                more_button = InlineKeyboardButton(
                    text="📋 More options...",
                    callback_data="show_more_options"
                )
                truncated_rows.append([more_button])

            return InlineKeyboardMarkup(inline_keyboard=truncated_rows)

        elif isinstance(markup, ReplyKeyboardMarkup):
            truncated_rows = []
            button_count = 0

            for row in markup.keyboard:
                if button_count + len(row) <= max_buttons:
                    truncated_rows.append(row)
                    button_count += len(row)
                else:
                    remaining_slots = max_buttons - button_count
                    if remaining_slots > 0:
                        truncated_rows.append(row[:remaining_slots])
                    break

            return ReplyKeyboardMarkup(keyboard=truncated_rows, resize_keyboard=markup.resize_keyboard)

        return markup
    except Exception as e:
        logger.error(f"Error truncating keyboard: {e}")
        return markup


def _validate_and_fix_markup(markup: Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]) -> Union[InlineKeyboardMarkup, ReplyKeyboardMarkup, None]:
    """Validate and potentially fix reply markup that's too large."""
    if not markup:
        return markup

    try:
        # Check button count first (faster check)
        button_count = _count_keyboard_buttons(markup)
        if button_count > MAX_KEYBOARD_BUTTONS:
            logger.warning(f"Keyboard has {button_count} buttons, truncating to {MAX_KEYBOARD_BUTTONS}")
            markup = _truncate_keyboard(markup, MAX_KEYBOARD_BUTTONS)

        # Check estimated size
        estimated_size = _estimate_markup_size(markup)
        if estimated_size > MAX_REPLY_MARKUP_SIZE:
            logger.warning(f"Keyboard size ({estimated_size} bytes) exceeds limit, truncating further")
            # Try with fewer buttons
            reduced_max = max(10, MAX_KEYBOARD_BUTTONS // 2)
            markup = _truncate_keyboard(markup, reduced_max)

            # Check again
            new_size = _estimate_markup_size(markup)
            if new_size > MAX_REPLY_MARKUP_SIZE:
                logger.error(f"Keyboard still too large ({new_size} bytes) after truncation, removing markup")
                return None

        return markup
    except Exception as e:
        logger.error(f"Error validating markup: {e}")
        return None


def _serialize_markup_to_dict(
    markup: Union[InlineKeyboardMarkup, ReplyKeyboardMarkup],
) -> Optional[Dict[str, Any]]:
    """Helper to convert aiogram markup objects to Telegram API JSON."""
    if not markup:
        return None
    try:
        # Use model_dump for modern Pydantic v2+ based aiogram versions
        if hasattr(markup, "model_dump"):
            # exclude_none=True is important to avoid sending null fields
            # mode='json' ensures types like Url are strings
            dumped = markup.model_dump(mode="json", exclude_none=True)
            # Ensure keys like 'inline_keyboard' or 'keyboard' exist
            if "inline_keyboard" in dumped or "keyboard" in dumped:
                return dumped
            else:
                logger.warning(f"Serialized markup missing keyboard fields: {dumped}")
                return None  # Or handle ReplyKeyboardRemove etc. if needed
        # Fallback for older versions or different serialization methods
        elif hasattr(markup, "to_python"):
            return markup.to_python()
        else:
            logger.warning(f"Cannot serialize unsupported markup type: {type(markup)}")
            return None
    except Exception as e:
        logger.error(f"Error serializing reply_markup: {e}", exc_info=True)
        return None


def validate_keyboard_size(markup: Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]) -> Dict[str, Any]:
    """
    Validate a keyboard and return information about its size and validity.

    Args:
        markup: The keyboard markup to validate

    Returns:
        Dict containing validation results:
        - valid: bool - Whether the keyboard is valid
        - button_count: int - Number of buttons
        - estimated_size: int - Estimated size in bytes
        - needs_truncation: bool - Whether truncation is needed
        - truncated_markup: Optional markup - Truncated version if needed
    """
    if not markup:
        return {
            "valid": True,
            "button_count": 0,
            "estimated_size": 0,
            "needs_truncation": False,
            "truncated_markup": None
        }

    button_count = _count_keyboard_buttons(markup)
    estimated_size = _estimate_markup_size(markup)
    needs_truncation = button_count > MAX_KEYBOARD_BUTTONS or estimated_size > MAX_REPLY_MARKUP_SIZE

    truncated_markup = None
    if needs_truncation:
        truncated_markup = _validate_and_fix_markup(markup)

    return {
        "valid": not needs_truncation or truncated_markup is not None,
        "button_count": button_count,
        "estimated_size": estimated_size,
        "needs_truncation": needs_truncation,
        "truncated_markup": truncated_markup
    }


def sync_send_message(
    chat_id: Union[int, str],
    text: str,
    reply_markup: Optional[Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]] = None,
    parse_mode: Optional[str] = "HTML",
    disable_web_page_preview: bool = True,
    bot: Optional[Bot] = None,
    # Allow passing token directly if bot object is unavailable/problematic
    token: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    try:
        import json
        import requests
        import time
    except ImportError:
        logger.error(
            "sync_send_message requires the 'requests' library. Please install it."
        )
        return None

    # --- Get Bot Token ---
    bot_token = token  # Prioritize explicitly passed token
    if not bot_token:
        # Try fetching from bot object if provided
        if bot and hasattr(bot, "token"):
            # Accessing bot.token is generally safe if bot is initialized
            bot_token = bot.token
        else:
            # Fallback: Try importing from a config module (adapt path as needed)
            try:
                from config import TELEGRAM_BOT_TOKEN

                bot_token = TELEGRAM_BOT_TOKEN
            except ImportError:
                logger.error(
                    "sync_send_message: Bot token not found. Provide 'token' param, 'bot' object, or define TELEGRAM_BOT_TOKEN in config."
                )
                return None

    if not bot_token:  # Should be redundant after checks, but as a safeguard
        logger.error("sync_send_message: Failed to acquire bot token.")
        return None

    logger.info(f"Attempting to send message synchronously to {chat_id}")

    # --- Prepare Text ---
    if len(text) > MAX_MESSAGE_LENGTH:
        logger.warning(
            f"sync_send_message: Truncating message text exceeding {MAX_MESSAGE_LENGTH} chars."
        )
        text = (
            text[: MAX_MESSAGE_LENGTH - 30] + "... [truncated]"
        )  # Ensure space for suffix

    if parse_mode == "HTML":
        original_text = text
        text = sanitize_html(text)
        if text != original_text:
            logger.debug("sync_send_message: HTML content was sanitized.")
        # Telegram might still reject complex or improperly closed sanitized HTML
        # If 'parse error' occurs, retrying without parse_mode might be needed

    # --- Prepare API Request ---
    api_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    headers = {"Content-Type": "application/json"}
    payload: Dict[str, Any] = {
        "chat_id": str(chat_id),  # Ensure chat_id is string for JSON
        "text": text,
        "disable_web_page_preview": disable_web_page_preview,
    }
    if parse_mode:
        payload["parse_mode"] = parse_mode

    # Serialize markup if provided
    markup_dict = _serialize_markup_to_dict(reply_markup)
    if markup_dict:
        try:
            payload["reply_markup"] = json.dumps(markup_dict)  # Must be JSON string
            logger.debug(
                f"sync_send_message: Using reply_markup: {payload['reply_markup']}"
            )
        except (TypeError, ValueError) as json_err:
            logger.error(
                f"sync_send_message: Failed to JSON encode reply_markup: {json_err}. Sending without markup."
            )
            # Continue without markup

    # --- Send Request with Retries ---
    max_retries = 3
    base_backoff = 1  # seconds

    for attempt in range(max_retries):
        try:
            response = requests.post(
                api_url, headers=headers, json=payload, timeout=30
            )  # Use json parameter
            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)

            response_json = response.json()

            if response_json.get("ok") is True:
                logger.info(
                    f"sync_send_message: Successfully sent message to {chat_id}."
                )
                return response_json.get("result")
            else:
                # Telegram API returned ok: false
                error_code = response_json.get("error_code")
                error_desc = response_json.get("description", "Unknown Telegram error")
                logger.error(
                    f"sync_send_message: Telegram API error {error_code}: {error_desc}"
                )

                # Check for permanent errors (no retry)
                if error_code == 400 and "chat not found" in error_desc.lower():
                    return None
                if error_code == 403 and (
                    "forbidden: bot was blocked" in error_desc.lower()
                    or "user is deactivated" in error_desc.lower()
                ):
                    return None
                if (
                    error_code == 400
                    and "can't parse entities" in error_desc.lower()
                    and payload.get("parse_mode")
                ):
                    logger.warning(
                        "sync_send_message: Retrying without parse_mode due to entity parsing error."
                    )
                    payload.pop("parse_mode")  # Modify payload for next attempt
                    # Don't increase backoff significantly for this specific retry
                    time.sleep(0.5)
                    continue  # Retry immediately without parse_mode

                # If it's a potentially temporary error, proceed to backoff/retry logic
                if attempt == max_retries - 1:  # Last attempt failed
                    return None  # Give up

        except requests.exceptions.Timeout:
            logger.warning(
                f"sync_send_message: Request timed out (attempt {attempt + 1}/{max_retries})."
            )
            if attempt == max_retries - 1:
                return None
        except requests.exceptions.RequestException as e:
            # Includes connection errors, invalid URL, etc.
            logger.error(
                f"sync_send_message: HTTP request error (attempt {attempt + 1}/{max_retries}): {e}"
            )
            # Don't retry on fundamental issues like invalid URL immediately? Maybe check error type.
            # For now, retry generic request errors.
            if attempt == max_retries - 1:
                return None
        except Exception as e:
            # Catch unexpected errors during request/response handling
            logger.error(
                f"sync_send_message: Unexpected error (attempt {attempt + 1}/{max_retries}): {e}",
                exc_info=True,
            )
            if attempt == max_retries - 1:
                return None

        # Exponential backoff if we haven't returned or continued
        backoff_time = base_backoff * (2**attempt)
        logger.info(f"sync_send_message: Retrying in {backoff_time:.2f} seconds...")
        time.sleep(backoff_time)

    logger.error(
        f"sync_send_message: Failed to send message to {chat_id} after {max_retries} attempts."
    )
    return None


@rate_limit_message_to_chat()
async def safe_edit_message(
    message,
    text,
    photo_url=None,
    reply_markup=None,
    parse_mode="HTML",
    is_tg_file=False,
):
    try:
        # Add null check for text - prevent empty message error
        # if text is None or (isinstance(text, str) and text.strip() == ""):
        #     logger.warning(
        #         "Empty message text provided to safe_edit_message, using fallback text"
        #     )
        #     text = "⚠️ Message content unavailable"

        # Validate and potentially fix reply markup before processing
        if reply_markup:
            original_markup = reply_markup
            reply_markup = _validate_and_fix_markup(reply_markup)
            if reply_markup != original_markup:
                if reply_markup is None:
                    logger.warning("Reply markup was too large and has been removed")
                else:
                    logger.info("Reply markup was truncated to fit size limits")

        # Compare reply markups properly - handles both None and actual markup objects
        current_markup = getattr(message, "reply_markup", None)
        markups_identical = False

        # Check if both markups are None or both are not None
        if current_markup is None and reply_markup is None:
            markups_identical = True
        # If current_markup exists and has an inline_keyboard attribute
        elif (
            current_markup is not None
            and reply_markup is not None
            and hasattr(current_markup, "inline_keyboard")
            and hasattr(reply_markup, "inline_keyboard")
        ):
            # Compare inline_keyboard structures more reliably
            try:
                # Convert both keyboards to a standardized format for comparison
                def normalize_keyboard(kb):
                    if not hasattr(kb, "inline_keyboard"):
                        return None
                    result = []
                    for row in kb.inline_keyboard:
                        normalized_row = []
                        for btn in row:
                            # Extract the key properties for comparison
                            normalized_btn = {
                                "text": getattr(btn, "text", None),
                                "callback_data": getattr(btn, "callback_data", None),
                                "url": getattr(btn, "url", None),
                            }
                            normalized_row.append(normalized_btn)
                        result.append(normalized_row)
                    return (
                        result  # Return the actual structure, not string representation
                    )

                current_kb_normalized = normalize_keyboard(current_markup)
                new_kb_normalized = normalize_keyboard(reply_markup)

                # Compare the structures (lists of lists of dictionaries)
                if current_kb_normalized == new_kb_normalized:
                    markups_identical = True
                    logger.debug("Reply markup is identical based on deep comparison")
                else:
                    # For debugging purposes
                    import json

                    # logger.debug(
                    #     f"Keyboards differ: Current={json.dumps(current_kb_normalized)}, New={json.dumps(new_kb_normalized)}"
                    # )
                    markups_identical = False
            except Exception as markup_err:
                logger.debug(
                    f"Error comparing markups: {markup_err}, assuming different"
                )
                # If comparison fails, assume they're different to be safe
                markups_identical = False

        # First, check if the message content is identical to avoid "message is not modified" errors
        if hasattr(message, "text") and message.text == text and markups_identical:
            logger.info(
                "Text message is identical (both text and markup), no changes needed"
            )
            return message

        # If this is a media message with caption, check if caption is identical
        if (
            hasattr(message, "caption")
            and message.caption == text
            and not photo_url
            and markups_identical
        ):  # No need to check if we're replacing the photo
            logger.info(
                "Media caption is identical (both caption and markup), no changes needed"
            )
            return message

        # Truncate caption/text if needed based on message type
        # For messages with media (photo, video, etc.), use MAX_CAPTION_LENGTH
        is_media_message = (
            (hasattr(message, "photo") and message.photo)
            or (hasattr(message, "video") and message.video)
            or (hasattr(message, "document") and message.document)
            or (hasattr(message, "animation") and message.animation)
            or (hasattr(message, "voice") and message.voice)
            or (hasattr(message, "audio") and message.audio)
        )

        max_length = MAX_CAPTION_LENGTH if is_media_message else MAX_MESSAGE_LENGTH
        truncate_suffix = (
            "[Caption truncated]"
            if is_media_message
            else "[Message truncated due to length]"
        )

        if len(text) > max_length:
            logger.warning(f"Text exceeds {max_length} characters, truncating...")
            text = text[: max_length - 50] + f"...\n\n{truncate_suffix}"

        # Sanitize HTML content if using HTML parse mode
        if parse_mode == "HTML" and text:
            original_text = text
            text = sanitize_html(text)
            if text != original_text:
                logger.info("HTML content was sanitized for Telegram API compatibility")

        # Check if message has photos and new photo is provided
        if hasattr(message, "photo") and message.photo and photo_url:
            try:
                # If it's a Telegram file ID
                if is_tg_file:
                    return await message.edit_media(
                        InputMediaPhoto(
                            media=photo_url, caption=text, parse_mode=parse_mode
                        ),
                        reply_markup=reply_markup,
                    )
                # If it's a URL
                else:
                    return await message.edit_media(
                        InputMediaPhoto(
                            media=photo_url, caption=text, parse_mode=parse_mode
                        ),
                        reply_markup=reply_markup,
                    )
            except TelegramAPIError as e:
                error_message = str(e).lower()
                # Handle "message is not modified" error
                if "message is not modified" in error_message:
                    logger.info("Message content is identical, no changes needed")
                    return (
                        message  # Return the original message as it's already correct
                    )
                # Handle message not found error specifically
                elif (
                    "message to edit not found" in error_message
                    or "message identifier is not specified" in error_message
                ):
                    logger.warning("Message to edit not found, sending as new message")
                    try:
                        # Send as a new message instead
                        return await message.bot.send_photo(
                            chat_id=message.chat.id,
                            photo=photo_url,
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(f"Error sending new photo message: {inner_e}")
                        return None
                else:
                    # Handle other errors
                    logger.error(f"Error editing media message: {e}")
                    # Try deleting and sending new message as fallback
                    try:
                        chat_id = message.chat.id
                        await message.delete()
                        return await message.bot.send_photo(
                            chat_id=chat_id,
                            photo=photo_url,
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(f"Error in fallback for media editing: {inner_e}")
                        return None
            except Exception as e:
                logger.error(f"Unexpected error in media editing: {e}")
                return None

        # Check if it's a photo message but we're not updating the photo
        elif hasattr(message, "photo") and message.photo and not photo_url:
            try:
                # Use edit_caption for photo messages when only updating text
                return await message.edit_caption(
                    caption=text, reply_markup=reply_markup, parse_mode=parse_mode
                )
            except TelegramAPIError as e:
                error_message = str(e).lower()
                # Handle "message is not modified" error
                if "message is not modified" in error_message:
                    logger.info("Caption is identical, no changes needed")
                    return (
                        message  # Return the original message as it's already correct
                    )
                # Handle message not found error specifically
                elif (
                    "message to edit not found" in error_message
                    or "message identifier is not specified" in error_message
                ):
                    logger.warning("Message to edit not found, sending as new message")
                    try:
                        # Get the file_id if possible
                        old_photo = None
                        if hasattr(message, "photo") and message.photo:
                            old_photo = message.photo[-1].file_id

                        # Send as a new message
                        return await message.bot.send_photo(
                            chat_id=message.chat.id,
                            photo=old_photo or "https://via.placeholder.com/150",
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(f"Error sending new photo message: {inner_e}")
                        return None
                else:
                    # Handle other errors
                    logger.error(f"Error editing caption: {e}")
                    # Fallback to delete and resend if caption edit fails
                    try:
                        chat_id = message.chat.id
                        old_photo = message.photo[
                            -1
                        ].file_id  # Get the largest photo's file_id
                        await message.delete()
                        return await message.bot.send_photo(
                            chat_id=chat_id,
                            photo=old_photo,
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(
                            f"Error in fallback for caption editing: {inner_e}"
                        )
                        return None
            except Exception as e:
                logger.error(f"Unexpected error in caption editing: {e}")
                return None

        # Check if it's a video message
        elif hasattr(message, "video") and message.video:
            try:
                # Use edit_caption for video messages when updating text
                return await message.edit_caption(
                    caption=text, reply_markup=reply_markup, parse_mode=parse_mode
                )
            except TelegramAPIError as e:
                error_message = str(e).lower()
                # Handle "message is not modified" error
                if "message is not modified" in error_message:
                    logger.info("Video caption is identical, no changes needed")
                    return (
                        message  # Return the original message as it's already correct
                    )
                elif (
                    "message to edit not found" in error_message
                    or "message identifier is not specified" in error_message
                ):
                    logger.warning("Message to edit not found, sending as new message")
                    try:
                        # Send as a new message
                        video_id = None
                        if hasattr(message, "video"):
                            video_id = message.video.file_id

                        if video_id:
                            return await message.bot.send_video(
                                chat_id=message.chat.id,
                                video=video_id,
                                caption=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                        else:
                            # Fallback to plain message if we can't get the video
                            return await message.bot.send_message(
                                chat_id=message.chat.id,
                                text=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                    except Exception as inner_e:
                        logger.error(f"Error sending new message: {inner_e}")
                        return None
                else:
                    # Handle other errors
                    logger.error(f"Error editing video caption: {e}")
                    # Fallback to delete and resend if caption edit fails
                    try:
                        chat_id = message.chat.id
                        video_id = message.video.file_id
                        await message.delete()
                        return await message.bot.send_video(
                            chat_id=chat_id,
                            video=video_id,
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(
                            f"Error in fallback for video caption editing: {inner_e}"
                        )
                        return None

        # Handle document messages
        elif hasattr(message, "document") and message.document:
            try:
                # Use edit_caption for document messages when updating text
                return await message.edit_caption(
                    caption=text, reply_markup=reply_markup, parse_mode=parse_mode
                )
            except TelegramAPIError as e:
                error_message = str(e).lower()
                if "message is not modified" in error_message:
                    logger.info("Document caption is identical, no changes needed")
                    return message
                elif (
                    "message to edit not found" in error_message
                    or "message identifier is not specified" in error_message
                ):
                    logger.warning("Message to edit not found, sending as new message")
                    try:
                        # Send as a new message
                        document_id = None
                        if hasattr(message, "document"):
                            document_id = message.document.file_id

                        if document_id:
                            return await message.bot.send_document(
                                chat_id=message.chat.id,
                                document=document_id,
                                caption=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                        else:
                            return await message.bot.send_message(
                                chat_id=message.chat.id,
                                text=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                    except Exception as inner_e:
                        logger.error(f"Error sending new document message: {inner_e}")
                        return None
                else:
                    logger.error(f"Error editing document caption: {e}")
                    try:
                        chat_id = message.chat.id
                        document_id = message.document.file_id
                        await message.delete()
                        return await message.bot.send_document(
                            chat_id=chat_id,
                            document=document_id,
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(
                            f"Error in fallback for document caption editing: {inner_e}"
                        )
                        return None

        # Handle animation messages
        elif hasattr(message, "animation") and message.animation:
            try:
                # Use edit_caption for animation messages when updating text
                return await message.edit_caption(
                    caption=text, reply_markup=reply_markup, parse_mode=parse_mode
                )
            except TelegramAPIError as e:
                error_message = str(e).lower()
                if "message is not modified" in error_message:
                    logger.info("Animation caption is identical, no changes needed")
                    return message
                elif (
                    "message to edit not found" in error_message
                    or "message identifier is not specified" in error_message
                ):
                    logger.warning("Message to edit not found, sending as new message")
                    try:
                        # Send as a new message
                        animation_id = None
                        if hasattr(message, "animation"):
                            animation_id = message.animation.file_id

                        if animation_id:
                            return await message.bot.send_animation(
                                chat_id=message.chat.id,
                                animation=animation_id,
                                caption=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                        else:
                            return await message.bot.send_message(
                                chat_id=message.chat.id,
                                text=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                    except Exception as inner_e:
                        logger.error(f"Error sending new animation message: {inner_e}")
                        return None
                else:
                    logger.error(f"Error editing animation caption: {e}")
                    try:
                        chat_id = message.chat.id
                        animation_id = message.animation.file_id
                        await message.delete()
                        return await message.bot.send_animation(
                            chat_id=chat_id,
                            animation=animation_id,
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(
                            f"Error in fallback for animation caption editing: {inner_e}"
                        )
                        return None

        # Handle audio messages
        elif hasattr(message, "audio") and message.audio:
            try:
                # Use edit_caption for audio messages when updating text
                return await message.edit_caption(
                    caption=text, reply_markup=reply_markup, parse_mode=parse_mode
                )
            except TelegramAPIError as e:
                error_message = str(e).lower()
                if "message is not modified" in error_message:
                    logger.info("Audio caption is identical, no changes needed")
                    return message
                elif (
                    "message to edit not found" in error_message
                    or "message identifier is not specified" in error_message
                ):
                    logger.warning("Message to edit not found, sending as new message")
                    try:
                        # Send as a new message
                        audio_id = None
                        if hasattr(message, "audio"):
                            audio_id = message.audio.file_id

                        if audio_id:
                            return await message.bot.send_audio(
                                chat_id=message.chat.id,
                                audio=audio_id,
                                caption=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                        else:
                            return await message.bot.send_message(
                                chat_id=message.chat.id,
                                text=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                    except Exception as inner_e:
                        logger.error(f"Error sending new audio message: {inner_e}")
                        return None
                else:
                    logger.error(f"Error editing audio caption: {e}")
                    try:
                        chat_id = message.chat.id
                        audio_id = message.audio.file_id
                        await message.delete()
                        return await message.bot.send_audio(
                            chat_id=chat_id,
                            audio=audio_id,
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(
                            f"Error in fallback for audio caption editing: {inner_e}"
                        )
                        return None

        # Handle voice messages
        elif hasattr(message, "voice") and message.voice:
            try:
                # Use edit_caption for voice messages when updating text
                return await message.edit_caption(
                    caption=text, reply_markup=reply_markup, parse_mode=parse_mode
                )
            except TelegramAPIError as e:
                error_message = str(e).lower()
                if "message is not modified" in error_message:
                    logger.info("Voice caption is identical, no changes needed")
                    return message
                elif (
                    "message to edit not found" in error_message
                    or "message identifier is not specified" in error_message
                ):
                    logger.warning("Message to edit not found, sending as new message")
                    try:
                        # Send as a new message
                        voice_id = None
                        if hasattr(message, "voice"):
                            voice_id = message.voice.file_id

                        if voice_id:
                            return await message.bot.send_voice(
                                chat_id=message.chat.id,
                                voice=voice_id,
                                caption=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                        else:
                            return await message.bot.send_message(
                                chat_id=message.chat.id,
                                text=text,
                                reply_markup=reply_markup,
                                parse_mode=parse_mode,
                            )
                    except Exception as inner_e:
                        logger.error(f"Error sending new voice message: {inner_e}")
                        return None
                else:
                    logger.error(f"Error editing voice caption: {e}")
                    try:
                        chat_id = message.chat.id
                        voice_id = message.voice.file_id
                        await message.delete()
                        return await message.bot.send_voice(
                            chat_id=chat_id,
                            voice=voice_id,
                            caption=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(
                            f"Error in fallback for voice caption editing: {inner_e}"
                        )
                        return None

        # Check if message has text content before attempting to edit it
        elif hasattr(message, "text") and message.text:
            # Standard text message edit
            try:
                return await message.edit_text(
                    text, reply_markup=reply_markup, parse_mode=parse_mode
                )
            except TelegramAPIError as e:
                error_message = str(e).lower()
                # Handle "message is not modified" error
                if "message is not modified" in error_message:
                    logger.info("Message text is identical, no changes needed")
                    return (
                        message  # Return the original message as it's already correct
                    )
                elif (
                    "message to edit not found" in error_message
                    or "message identifier is not specified" in error_message
                    or "there is no text in the message to edit" in error_message
                ):
                    logger.warning(
                        f"Cannot edit message: {error_message}, sending as new message"
                    )
                    try:
                        # Send as a new message
                        return await message.bot.send_message(
                            chat_id=message.chat.id,
                            text=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(f"Error sending new message: {inner_e}")
                        return None
                else:
                    logger.error(f"Error editing text message: {e}")
                    # Try to send a new message without deleting the original
                    try:
                        return await message.bot.send_message(
                            chat_id=message.chat.id,
                            text=text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                        )
                    except Exception as inner_e:
                        logger.error(f"Error in fallback for text editing: {inner_e}")
                        return None
        else:
            # If there's no text to edit or it's an unknown message type,
            # Send as a new message
            logger.warning(
                f"Message has no text to edit or is unsupported type: {type(message).__name__}, sending as new message"
            )
            try:
                # Try to delete the original message first to avoid confusion
                try:
                    await message.delete()
                except Exception as delete_error:
                    logger.warning(f"Could not delete original message: {delete_error}")
                    # Non-critical error, continue with sending new message

                return await message.bot.send_message(
                    chat_id=message.chat.id,
                    text=text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                )
            except Exception as fallback_e:
                logger.error(f"Error sending new message: {fallback_e}")
                return None
    except TelegramAPIError as e:
        error_message = str(e).lower()
        # Handle "message is not modified" error at the top level
        if "message is not modified" in error_message:
            # This is not an actual error, just return the original message
            logger.debug(
                "Message is identical, no changes needed"
            )  # Downgrade to debug from info
            return message  # Return the original message as it's already correct
        elif "reply markup is too long" in error_message or "bad request: reply markup is too long" in error_message:
            logger.warning("Reply markup is too long, attempting to fix and retry")
            try:
                # Try to fix the markup by removing it entirely
                fixed_markup = None
                if reply_markup:
                    # Try with a heavily truncated version first
                    fixed_markup = _truncate_keyboard(reply_markup, max_buttons=10)
                    if _estimate_markup_size(fixed_markup) > MAX_REPLY_MARKUP_SIZE:
                        fixed_markup = None

                # Retry with fixed markup
                if hasattr(message, "text") and message.text:
                    return await message.edit_text(
                        text=text,
                        reply_markup=fixed_markup,
                        parse_mode=parse_mode,
                    )
                else:
                    # Send as new message if editing fails
                    return await message.bot.send_message(
                        chat_id=message.chat.id,
                        text=text,
                        reply_markup=fixed_markup,
                        parse_mode=parse_mode,
                    )
            except Exception as inner_e:
                logger.error(f"Error in reply markup fallback: {inner_e}")
                # Final fallback - send without any markup
                try:
                    return await message.bot.send_message(
                        chat_id=message.chat.id,
                        text=text,
                        parse_mode=parse_mode,
                    )
                except Exception as final_e:
                    logger.error(f"Error in final fallback: {final_e}")
                    return None
        elif (
            "message to edit not found" in error_message
            or "message identifier is not specified" in error_message
            or "there is no text in the message to edit" in error_message
        ):
            logger.warning("Message to edit not found, sending as new message")
            try:
                # Send as a new message
                return await message.bot.send_message(
                    chat_id=message.chat.id,
                    text=text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                )
            except Exception as inner_e:
                logger.error(f"Error sending new message: {inner_e}")
                return None
        else:
            logger.error(f"Error in safe_edit_message: {e}")
            logger.error(traceback.format_exc())
            try:
                # Last-ditch effort - send a new message
                return await message.bot.send_message(
                    chat_id=message.chat.id,
                    text=text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                )
            except Exception:
                return None
    except Exception as e:
        logger.error(f"Unexpected error in safe_edit_message: {e}")
        logger.error(traceback.format_exc())
        try:
            # Last-ditch effort - send a new message
            return await message.bot.send_message(
                chat_id=message.chat.id,
                text=text,
                reply_markup=reply_markup,
                parse_mode=parse_mode,
            )
        except Exception:
            return None


@rate_limit_message_to_chat()
async def safe_send_message(
    chat_id: Union[int, str],
    text: str,
    reply_markup: Optional[Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]] = None,
    parse_mode: Optional[str] = "HTML",
    disable_web_page_preview: bool = True,
    bot: Optional[Bot] = None,
) -> Union[Message, None]:
    """
    Safely send a message to a Telegram chat with proper error handling and task context management.

    This function ensures that timeouts are properly handled inside a task context.
    """
    # Apply text truncation if needed
    if len(text) > MAX_MESSAGE_LENGTH:
        logger.warning(
            f"Message text exceeds {MAX_MESSAGE_LENGTH} characters, truncating..."
        )
        text = (
            text[: MAX_MESSAGE_LENGTH - 100]
            + "...\n\n[Message truncated due to length]"
        )

    # Validate and potentially fix reply markup
    if reply_markup:
        original_markup = reply_markup
        reply_markup = _validate_and_fix_markup(reply_markup)
        if reply_markup != original_markup:
            if reply_markup is None:
                logger.warning("Reply markup was too large and has been removed")
            else:
                logger.info("Reply markup was truncated to fit size limits")

    # Sanitize HTML content if using HTML parse mode
    if parse_mode == "HTML":
        original_text = text
        text = sanitize_html(text)
        if text != original_text:
            logger.info("HTML content was sanitized for Telegram API compatibility")

    try:
        # Make sure we have a valid event loop
        current_loop = get_event_loop()
        if threading.current_thread().name != "MainThread":
            logger.debug(
                f"Sending message from thread: {threading.current_thread().name}"
            )

        # Simple direct function - no timeout handling needed here
        # We'll handle any timeout errors at the caller's level
        result = await bot.send_message(
            chat_id,
            text,
            reply_markup=reply_markup,
            parse_mode=parse_mode,
            disable_web_page_preview=disable_web_page_preview,
        )
        return result

    except asyncio.TimeoutError:
        logger.warning(
            f"Timeout sending message to {chat_id}, retrying without timeout"
        )
        try:
            # Try again with a simple direct call
            return await bot.send_message(
                chat_id,
                text,
                reply_markup=reply_markup,
                parse_mode=parse_mode,
                disable_web_page_preview=disable_web_page_preview,
            )
        except Exception as e:
            logger.error(f"Failed to send message after timeout: {e}")
            return None
    except RuntimeError as e:
        if "Event loop is closed" in str(e):
            logger.warning("Event loop was closed, creating a new one")
            if hasattr(thread_local, "loop"):
                del thread_local.loop
            loop = get_event_loop()  # Get a fresh loop

            try:
                # Simple direct call in the new loop
                return await bot.send_message(
                    chat_id,
                    text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                    disable_web_page_preview=disable_web_page_preview,
                )
            except Exception as retry_err:
                logger.error(
                    f"Failed to send message even with new event loop: {retry_err}"
                )
                return None
        elif "Timeout context manager should be used inside a task" in str(e):
            # This is the specific error we're trying to fix
            logger.warning(
                "Caught timeout context error, using direct send without any timeout handlers"
            )
            try:
                # Most direct approach possible - direct call with no timeout handling
                # We're avoiding any asyncio timeout mechanisms completely
                return await bot.send_message(
                    chat_id,
                    text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                    disable_web_page_preview=disable_web_page_preview,
                )
            except Exception as direct_err:
                logger.error(f"Failed with direct API call: {direct_err}")
                # Last resort fallback - try with minimal wrapping
                try:
                    # Create and run a task directly - no wait_for or timeout
                    task = asyncio.create_task(
                        bot.send_message(
                            chat_id,
                            text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                            disable_web_page_preview=disable_web_page_preview,
                        )
                    )
                    return await task
                except Exception as last_err:
                    logger.error(f"All fallback attempts failed: {last_err}")
                    return None
        else:
            logger.error(f"Runtime error sending message to {chat_id}: {e}")
            return None
    except TelegramAPIError as e:
        error_message = str(e).lower()

        if "bot was blocked by the user" in error_message:
            logger.info(f"User {chat_id} has blocked the bot")
            return None
        elif "chat not found" in error_message:
            logger.info(f"Chat {chat_id} not found")
            return None
        elif "user is deactivated" in error_message:
            logger.info(f"User {chat_id} is deactivated")
            return None
        elif "reply markup is too long" in error_message or "bad request: reply markup is too long" in error_message:
            logger.warning("Reply markup is too long in safe_send_message, attempting to fix")
            try:
                # Try to fix the markup by removing it entirely or heavily truncating
                fixed_markup = None
                if reply_markup:
                    # Try with a heavily truncated version first
                    fixed_markup = _truncate_keyboard(reply_markup, max_buttons=10)
                    if _estimate_markup_size(fixed_markup) > MAX_REPLY_MARKUP_SIZE:
                        fixed_markup = None

                # Retry with fixed markup
                return await bot.send_message(
                    chat_id,
                    text,
                    reply_markup=fixed_markup,
                    parse_mode=parse_mode,
                    disable_web_page_preview=disable_web_page_preview,
                )
            except Exception as inner_e:
                logger.error(f"Error in reply markup fallback for send_message: {inner_e}")
                # Final fallback - send without any markup
                try:
                    return await bot.send_message(
                        chat_id,
                        text,
                        parse_mode=parse_mode,
                        disable_web_page_preview=disable_web_page_preview,
                    )
                except Exception as final_e:
                    logger.error(f"Error in final fallback for send_message: {final_e}")
                    return None
        elif "can't parse entities" in error_message:
            logger.warning(f"HTML parsing error: {e}")
            # Try again with more aggressive HTML sanitizing or without parse mode
            try:
                # First attempt: Try stripping all HTML tags
                sanitized_text = re.sub(r"<[^>]+>", "", text)

                # Direct call with no parse mode
                return await bot.send_message(
                    chat_id,
                    sanitized_text,
                    reply_markup=reply_markup,
                    parse_mode=None,  # Disable parse mode entirely
                    disable_web_page_preview=disable_web_page_preview,
                )
            except Exception as new_e:
                logger.error(f"Failed to send sanitized message: {new_e}")
                return None
        else:
            logger.error(f"Failed to send message to {chat_id}: {e}")
            return None
    except Exception as e:
        logger.error(f"Unexpected error sending message to {chat_id}: {e}")
        logger.error(traceback.format_exc())
        return None


@rate_limit_media()
async def safe_send_photo(
    chat_id: Union[int, str],
    photo: Union[str, FSInputFile, BufferedInputFile],
    caption: Optional[str] = None,
    reply_markup: Optional[Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]] = None,
    parse_mode: Optional[str] = "HTML",
    bot: Optional[Bot] = None,
) -> Union[Message, None]:
    # Truncate caption if needed
    if caption and len(caption) > MAX_CAPTION_LENGTH:
        logger.warning(
            f"Caption exceeds {MAX_CAPTION_LENGTH} characters, truncating..."
        )
        caption = caption[: MAX_CAPTION_LENGTH - 50] + "...\n\n[Caption truncated]"

    # Validate and potentially fix reply markup
    if reply_markup:
        original_markup = reply_markup
        reply_markup = _validate_and_fix_markup(reply_markup)
        if reply_markup != original_markup:
            if reply_markup is None:
                logger.warning("Reply markup was too large and has been removed from photo")
            else:
                logger.info("Reply markup was truncated to fit size limits for photo")

    # Sanitize HTML content in caption if using HTML parse mode
    if parse_mode == "HTML" and caption:
        original_caption = caption
        caption = sanitize_html(caption)
        if caption != original_caption:
            logger.info(
                "HTML content in caption was sanitized for Telegram API compatibility"
            )

    try:
        # Make sure we have a valid event loop in the current thread
        current_loop = get_event_loop()
        if threading.current_thread().name != "MainThread":
            logger.debug(
                f"Sending photo from thread: {threading.current_thread().name}"
            )

        # If photo is a string, properly handle file paths and URLs
        if isinstance(photo, str):
            # First, check if it's a URL
            if photo.startswith(("http://", "https://", "ftp://")):
                # It's a URL - handle URL encoding as before
                try:
                    from urllib.parse import urlparse, urlunparse, quote
                    import re

                    # Parse the URL
                    parsed_url = urlparse(photo)

                    # Check for empty hostname
                    if not parsed_url.netloc:
                        logger.warning(f"URL has no hostname: {photo}")
                        # Try to send as text instead
                        return await safe_send_message(
                            chat_id,
                            (
                                f"{caption}\n\n[Error: Invalid image URL]"
                                if caption
                                else "[Error: Invalid image URL]"
                            ),
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                            bot=bot,
                        )

                    # For URLs with Unicode or special characters, use urllib.parse.quote to encode properly
                    encoded_path = quote(parsed_url.path, safe="/")
                    encoded_query = quote(parsed_url.query, safe="=&?")

                    # Process international domain names and sanitize hostname
                    try:
                        # Handle hostname encoding - split by dots to process each part separately
                        hostname_parts = parsed_url.netloc.split(".")

                        # Apply IDNA encoding to each part of the hostname
                        cleaned_hostname_parts = []
                        for part in hostname_parts:
                            try:
                                # Try to encode as IDNA (for international domain names)
                                encoded_part = part.encode("idna").decode("ascii")
                                # Only keep valid hostname characters (alphanumeric, hyphen)
                                encoded_part = re.sub(
                                    r"[^a-zA-Z0-9\-\.]",
                                    "",
                                    encoded_part,  # Added dot to allowed characters
                                )
                                cleaned_hostname_parts.append(encoded_part)
                            except UnicodeError:
                                # Fallback for parts that can't be IDNA encoded
                                cleaned_part = re.sub(r"[^a-zA-Z0-9\-]", "", part)
                                cleaned_hostname_parts.append(cleaned_part)

                        cleaned_hostname = ".".join(cleaned_hostname_parts)

                        # Rebuild the URL with the cleaned hostname
                        encoded_url = urlunparse(
                            (
                                parsed_url.scheme,
                                cleaned_hostname,
                                encoded_path,
                                parsed_url.params,
                                encoded_query,
                                parsed_url.fragment,
                            )
                        )

                        logger.info(
                            f"Sanitized URL for Telegram compatibility: {encoded_url[:30]}..."
                        )
                    except Exception as hostname_error:
                        logger.warning(
                            f"Error sanitizing hostname: {hostname_error}, falling back to basic encoding"
                        )
                        # Basic fallback encoding
                        encoded_url = urlunparse(
                            (
                                parsed_url.scheme,
                                parsed_url.netloc,
                                encoded_path,
                                parsed_url.params,
                                encoded_query,
                                parsed_url.fragment,
                            )
                        )

                    # Final safety check for known problematic characters in the URL
                    if re.search(r'[\s<>"\'\\]', encoded_url):
                        logger.warning(
                            f"URL contains disallowed characters even after encoding: {encoded_url}"
                        )
                        # Try to send as text instead
                        return await safe_send_message(
                            chat_id,
                            (
                                f"{caption}\n\n[Error: URL contains invalid characters]"
                                if caption
                                else "[Error: URL contains invalid characters]"
                            ),
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                            bot=bot,
                        )

                    # Additional check for valid URL structure (must have proper scheme + hostname)
                    if not (
                        encoded_url.startswith(("http://", "https://"))
                        and len(encoded_url.split("://")[1]) > 0
                    ):
                        logger.warning(f"URL has invalid structure: {encoded_url}")
                        return await safe_send_message(
                            chat_id,
                            (
                                f"{caption}\n\n[Error: Invalid image URL structure]"
                                if caption
                                else "[Error: Invalid image URL structure]"
                            ),
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                            bot=bot,
                        )

                    # Use the encoded URL
                    photo = encoded_url
                    logger.info(
                        f"URL encoded for Telegram compatibility: {photo[:30]}..."
                    )

                except Exception as url_error:
                    logger.warning(f"Error encoding URL: {url_error}")
                    # Don't use potentially invalid URL, send a fallback message
                    return await safe_send_message(
                        chat_id,
                        (
                            f"{caption}\n\n[Error: Could not process image URL]"
                            if caption
                            else "[Error: Could not process image URL]"
                        ),
                        reply_markup=reply_markup,
                        parse_mode=parse_mode,
                        bot=bot,
                    )
            else:
                # It's either a local file path or a file_id
                import os

                # More comprehensive check for local file paths:
                # 1. Check for absolute paths (starts with / or drive letter:)
                # 2. Check for relative paths that might be in the uploads directory
                is_likely_file_path = (
                    photo.startswith("/")
                    or "\\" in photo
                    or (len(photo) > 1 and photo[1] == ":")
                    or photo.startswith("uploads/")
                    or "uploads\\" in photo
                )

                if is_likely_file_path:
                    # Try several possible path variations to find the file
                    found_file = False
                    possible_paths = [
                        photo,  # Try the path as is
                        os.path.normpath(photo),  # Normalize path separators
                        os.path.join(
                            os.getcwd(), photo
                        ),  # Try as relative to current dir
                    ]

                    # Also try with and without 'uploads' prefix if needed
                    if not photo.startswith(
                        ("uploads/", "uploads\\")
                    ) and not os.path.isabs(photo):
                        possible_paths.append(
                            os.path.join(os.getcwd(), "uploads", photo)
                        )

                    # If path starts with /uploads, try without the leading slash
                    if photo.startswith("/uploads/"):
                        possible_paths.append(os.path.join(os.getcwd(), photo[1:]))

                    # Try each path until we find the file
                    for path in possible_paths:
                        if os.path.isfile(path):
                            logger.info(f"Found file at: {path}")
                            photo = FSInputFile(path)
                            found_file = True
                            break

                    if not found_file:
                        logger.warning(
                            f"File not found at any of these locations: {possible_paths}"
                        )
                        # Send text fallback if file doesn't exist
                        return await safe_send_message(
                            chat_id,
                            (
                                f"{caption}\n\n[Error: Image file not found: {photo}]"
                                if caption
                                else f"[Error: Image file not found: {photo}]"
                            ),
                            reply_markup=reply_markup,
                            parse_mode=parse_mode,
                            bot=bot,
                        )
                else:
                    # If not a file path, assume it's a file_id
                    logger.info(f"Using what appears to be a file_id: {photo[:15]}...")

        # Try to send the photo with retries for common issues
        try:
            # Directly await the bot call
            return await bot.send_photo(
                chat_id,
                photo,
                caption=caption,
                reply_markup=reply_markup,
                parse_mode=parse_mode,
            )
        except TelegramBadRequest as url_err:
            # Specific handling for URL-related errors
            if "wrong HTTP URL specified" in str(url_err).lower():
                logger.warning(f"Invalid URL format detected: {url_err}")

                # Send text fallback when URL is invalid
                return await safe_send_message(
                    chat_id,
                    (
                        f"{caption}\n\n[Error: Image URL not compatible with Telegram]"
                        if caption
                        else "[Error: Image URL not compatible with Telegram]"
                    ),
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                    bot=bot,
                )
            else:
                # Re-raise for other TelegramBadRequest errors
                raise

    except RuntimeError as e:
        if "Event loop is closed" in str(e):
            logger.warning("Event loop was closed, creating a new one")
            # Force creation of a new event loop
            if hasattr(thread_local, "loop"):
                del thread_local.loop

            # Get a fresh event loop
            loop = get_event_loop()

            try:
                # Directly await the bot call again in the retry
                return await bot.send_photo(
                    chat_id,
                    photo,
                    caption=caption,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                )
            except Exception as retry_err:
                logger.error(
                    f"Failed to send photo even with new event loop: {retry_err}"
                )
                return None
        else:
            logger.error(f"Runtime error sending photo to {chat_id}: {e}")
            return None
    except TelegramAPIError as e:
        error_message = str(e).lower()

        if "bot was blocked by the user" in error_message:
            logger.info(f"User {chat_id} has blocked the bot")
            return None
        elif "chat not found" in error_message:
            logger.info(f"Chat {chat_id} not found")
            return None
        elif (
            "wrong file identifier" in error_message
            or "file identifier" in error_message
            or "wrong remote file id" in error_message
            or "cannot write request body" in error_message
            or "bad request: invalid file http url" in error_message
        ):
            logger.warning(
                f"Invalid photo URL/ID: {photo if isinstance(photo, str) else 'non-string photo'}"
            )
            # Try to send as text instead
            try:
                return await safe_send_message(
                    chat_id,
                    (
                        f"{caption}\n\n[Error: Could not load image]"
                        if caption
                        else "[Error: Could not load image]"
                    ),
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                    bot=bot,
                )
            except Exception as new_e:
                logger.error(f"Failed to send fallback message: {new_e}")
                return None
        elif "can't parse entities" in error_message:
            logger.warning(f"HTML parsing error in photo caption: {e}")
            # Try again with more aggressive HTML sanitizing or without parse mode
            try:
                # First attempt: Try stripping all HTML tags from caption
                sanitized_caption = re.sub(r"<[^>]+>", "", caption) if caption else None
                # Directly await the bot call in fallback
                return await bot.send_photo(
                    chat_id,
                    photo,
                    caption=sanitized_caption,
                    reply_markup=reply_markup,
                    parse_mode=None,  # Disable parse mode entirely
                )
            except Exception as new_e:
                logger.error(f"Failed to send photo with sanitized caption: {new_e}")
                return None
        else:
            logger.error(f"Failed to send photo to {chat_id}: {e}")
            # Log detailed error for debugging
            logger.error(f"Full error details: {traceback.format_exc()}")
            # Attempt one more time with a plain text message as ultimate fallback
            try:
                error_desc = str(e).split("\n")[0] if "\n" in str(e) else str(e)
                return await safe_send_message(
                    chat_id,
                    f"Unable to display image. {error_desc}\n\n{caption or ''}",
                    reply_markup=reply_markup,
                    parse_mode=None,
                    bot=bot,
                )
            except Exception:
                return None
    except Exception as e:
        logger.error(f"Unexpected error sending photo to {chat_id}: {e}")
        logger.error(traceback.format_exc())
        return None


def sync_call_async_function(async_func, *args, **kwargs):
    """
    Synchronously call an async function from a synchronous context.

    Args:
        async_func: The async function to call
        *args: Arguments to pass to the async function
        **kwargs: Keyword arguments to pass to the async function

    Returns:
        The result of the async function
    """
    try:
        return run_async(async_func(*args, **kwargs))
    except Exception as e:
        logger.error(
            f"Error in sync_call_async_function calling {async_func.__name__}: {e}"
        )
        return None
