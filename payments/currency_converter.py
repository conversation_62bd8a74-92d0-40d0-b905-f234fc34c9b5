import aiohttp
import logging
import json
import time
from typing import Dict, Any, Optional, Tuple, Union

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Cache for currency prices to avoid excessive API calls
_currency_price_cache = {"timestamp": 0, "prices": {}}

# Cache expiry time in seconds (5 minutes)
CACHE_EXPIRY_TIME = 300

# USD-pegged stablecoins that should be treated as $1.00 USD equivalent
USD_STABLECOINS = {
    "USDT",  # Tether
}

# Fixed USD price for stablecoins (in USD)
STABLECOIN_USD_PRICE = 1.0


def is_usd_stablecoin(currency: str) -> bool:
    """
    Check if a currency is a USD-pegged stablecoin.

    Args:
        currency: Currency code to check

    Returns:
        bool: True if the currency is a USD-pegged stablecoin
    """
    return currency.upper().strip() in USD_STABLECOINS


async def fetch_currency_prices() -> Dict[str, float]:
    """
    Fetch current cryptocurrency prices from OXA Pay API.

    Returns:
        dict: Dictionary mapping currency codes to their USD price
    """
    current_time = time.time()

    # Check if cache is still valid
    if (
        _currency_price_cache["timestamp"] > 0
        and current_time - _currency_price_cache["timestamp"] < CACHE_EXPIRY_TIME
        and _currency_price_cache["prices"]
    ):
        logger.info("Using cached currency prices")
        return _currency_price_cache["prices"]

    # Try multiple endpoints for price data
    endpoints = [
        {
            "url": "https://api.oxapay.com/v1/common/prices",
            "type": "oxapay",
            "name": "OXA Pay Prices",
        }
    ]

    logger.info("Fetching current currency prices from OXA Pay API")

    try:
        async with aiohttp.ClientSession() as session:
            for endpoint in endpoints:
                endpoint_url = endpoint["url"]
                endpoint_type = endpoint["type"]
                endpoint_name = endpoint["name"]

                try:
                    logger.debug(f"Trying endpoint: {endpoint_name} ({endpoint_url})")
                    # Reduce timeout for currency prices to 5 seconds for faster response
                    async with session.get(endpoint_url, timeout=5) as response:
                        if response.status == 200:
                            data = await response.json()
                            logger.debug(f"Response from {endpoint_name}: {data}")

                            # Extract prices from the response
                            prices = {}

                            if endpoint_type == "oxapay":
                                # Handle OXA Pay response formats
                                if data.get("status") == 200 and "data" in data:
                                    data_content = data["data"]

                                    # Format 1: List of dictionaries with currency and price
                                    if (
                                        isinstance(data_content, list)
                                        and len(data_content) > 0
                                    ):
                                        for item in data_content:
                                            if isinstance(item, dict):
                                                currency = item.get("currency")
                                                price = (
                                                    item.get("price")
                                                    or item.get("rate")
                                                    or item.get("value")
                                                )
                                                if currency and price:
                                                    try:
                                                        prices[currency] = float(price)
                                                    except (ValueError, TypeError):
                                                        logger.error(
                                                            f"Invalid price format for {currency}: {price}"
                                                        )
                                            else:
                                                # Skip string items (currency codes without prices)
                                                continue

                                    # Format 2: Dictionary with currency codes as keys
                                    elif isinstance(data_content, dict):
                                        for currency, price in data_content.items():
                                            if price and currency:
                                                try:
                                                    prices[currency] = float(price)
                                                except (ValueError, TypeError):
                                                    logger.error(
                                                        f"Invalid price format for {currency}: {price}"
                                                    )

                            elif endpoint_type == "coingecko":
                                # Handle CoinGecko response format
                                # Map CoinGecko IDs to currency codes
                                coingecko_mapping = {
                                    "bitcoin": "BTC",
                                    "ethereum": "ETH",
                                    "binancecoin": "BNB",
                                    "solana": "SOL",
                                    "polygon": "POL",
                                    "cardano": "ADA",
                                    "polkadot": "DOT",
                                    "avalanche-2": "AVAX",
                                    "chainlink": "LINK",
                                    "uniswap": "UNI",
                                    "litecoin": "LTC",
                                    "bitcoin-cash": "BCH",
                                    "ripple": "XRP",
                                    "dogecoin": "DOGE",
                                    "shiba-inu": "SHIB",
                                    "tron": "TRX",
                                    "tether": "USDT",
                                    "usd-coin": "USDC",
                                    "dai": "DAI",
                                }

                                for coin_id, price_data in data.items():
                                    if (
                                        isinstance(price_data, dict)
                                        and "usd" in price_data
                                    ):
                                        currency = coingecko_mapping.get(coin_id)
                                        if currency:
                                            try:
                                                prices[currency] = float(
                                                    price_data["usd"]
                                                )
                                            except (ValueError, TypeError):
                                                logger.error(
                                                    f"Invalid price format for {currency}: {price_data['usd']}"
                                                )

                            # If we got prices, use them
                            if prices:
                                _currency_price_cache["timestamp"] = current_time
                                _currency_price_cache["prices"] = prices
                                logger.info(
                                    f"Updated currency prices from {endpoint_name}: {len(prices)} currencies"
                                )
                                return prices
                            else:
                                logger.warning(
                                    f"No valid prices found in response from {endpoint_name}"
                                )
                        else:
                            error_text = await response.text()
                            logger.warning(
                                f"API error from {endpoint_name} - {response.status}: {error_text}"
                            )

                except aiohttp.ClientError as e:
                    logger.warning(f"HTTP error from {endpoint_name}: {e}")
                except Exception as e:
                    logger.warning(
                        f"Error processing response from {endpoint_name}: {e}"
                    )

            # If we reach here, all endpoints failed
            logger.error("All price endpoints failed to provide valid data")
            return {}

    except aiohttp.ClientError as e:
        logger.error(f"HTTP error fetching currency prices: {e}")
    except Exception as e:
        logger.exception(f"Unexpected error fetching currency prices: {e}")

    # If we reach here, use cached prices if available, otherwise return empty dict
    return _currency_price_cache.get("prices", {})


async def convert_currency(
    amount: Union[float, str], from_currency: str, to_currency: str = "USDT"
) -> Tuple[float, Dict[str, Any]]:
    """
    Convert an amount from one cryptocurrency to another.

    Args:
        amount: The amount to convert
        from_currency: Source currency code (e.g., "BNB")
        to_currency: Target currency code (e.g., "USDT")

    Returns:
        tuple: (converted_amount, conversion_info)
            - converted_amount: The converted amount as a float
            - conversion_info: Dictionary with conversion details
    """
    # Ensure amount is a float and validate range
    try:
        amount_float = float(amount)
    except (ValueError, TypeError):
        logger.error(f"Invalid amount format: {amount}")
        return 0.0, {
            "success": False,
            "error": "Invalid amount format",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount,
            "converted_amount": 0.0,
        }

    # Validate amount range and prevent negative values
    if amount_float < 0:
        logger.error(f"Negative amount not allowed: {amount_float}")
        return 0.0, {
            "success": False,
            "error": "Negative amounts are not allowed",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    # Check for zero amount
    if amount_float == 0:
        logger.warning(f"Zero amount conversion requested: {amount_float}")
        return 0.0, {
            "success": True,
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
            "exchange_rate": 0.0,
        }

    # Check for extremely large amounts (prevent overflow)
    MAX_AMOUNT = 1e12  # 1 trillion - reasonable upper limit
    if amount_float > MAX_AMOUNT:
        logger.error(f"Amount too large: {amount_float} (max: {MAX_AMOUNT})")
        return 0.0, {
            "success": False,
            "error": f"Amount too large (maximum: {MAX_AMOUNT:,.0f})",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    # Check for extremely small amounts (prevent precision issues)
    MIN_AMOUNT = 1e-8  # 0.00000001 - reasonable lower limit for crypto
    if amount_float < MIN_AMOUNT:
        logger.warning(f"Amount too small: {amount_float} (min: {MIN_AMOUNT})")
        return 0.0, {
            "success": False,
            "error": f"Amount too small (minimum: {MIN_AMOUNT:.8f})",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    # Validate currency codes
    if (
        not from_currency
        or not isinstance(from_currency, str)
        or len(from_currency.strip()) == 0
    ):
        logger.error(f"Invalid from_currency: {from_currency}")
        return 0.0, {
            "success": False,
            "error": "Invalid source currency code",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    if (
        not to_currency
        or not isinstance(to_currency, str)
        or len(to_currency.strip()) == 0
    ):
        logger.error(f"Invalid to_currency: {to_currency}")
        return 0.0, {
            "success": False,
            "error": "Invalid target currency code",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    # Sanitize currency codes (remove whitespace, convert to uppercase)
    from_currency = from_currency.strip().upper()
    to_currency = to_currency.strip().upper()

    # Validate currency code format (basic check for reasonable length and characters)
    import re

    currency_pattern = re.compile(r"^[A-Z]{2,10}$")  # 2-10 uppercase letters
    if not currency_pattern.match(from_currency):
        logger.error(f"Invalid from_currency format: {from_currency}")
        return 0.0, {
            "success": False,
            "error": f"Invalid source currency format: {from_currency}",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    if not currency_pattern.match(to_currency):
        logger.error(f"Invalid to_currency format: {to_currency}")
        return 0.0, {
            "success": False,
            "error": f"Invalid target currency format: {to_currency}",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    # Handle same currency case (currencies are already uppercase)
    if from_currency == to_currency:
        return amount_float, {
            "success": True,
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": amount_float,
            "exchange_rate": 1.0,
        }

    # Handle stablecoin to stablecoin conversions (all USD-pegged stablecoins are equivalent)
    if is_usd_stablecoin(from_currency) and is_usd_stablecoin(to_currency):
        logger.info(
            f"Converting between USD stablecoins: {amount_float} {from_currency} = {amount_float} {to_currency} "
            f"(1:1 ratio for USD-pegged stablecoins)"
        )
        return amount_float, {
            "success": True,
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": amount_float,
            "exchange_rate": 1.0,
            "stablecoin_conversion": True,
        }

    # Fetch current prices
    prices = await fetch_currency_prices()

    if not prices:
        logger.error("Failed to fetch currency prices")
        return 0.0, {
            "success": False,
            "error": "Failed to fetch currency prices",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    # Get source and target currency prices (currencies are already uppercase)
    # Use fixed price for USD stablecoins, API price for others
    if is_usd_stablecoin(from_currency):
        from_price = STABLECOIN_USD_PRICE
        logger.info(
            f"Using fixed USD price for stablecoin {from_currency}: ${from_price}"
        )
    else:
        from_price = prices.get(from_currency)

    if is_usd_stablecoin(to_currency):
        to_price = STABLECOIN_USD_PRICE
        logger.info(f"Using fixed USD price for stablecoin {to_currency}: ${to_price}")
    else:
        to_price = prices.get(to_currency)

    if not from_price:
        logger.error(f"Price not available for source currency: {from_currency}")
        return 0.0, {
            "success": False,
            "error": f"Price not available for source currency: {from_currency}",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    if not to_price:
        logger.error(f"Price not available for target currency: {to_currency}")
        return 0.0, {
            "success": False,
            "error": f"Price not available for target currency: {to_currency}",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": 0.0,
        }

    # Calculate exchange rate and converted amount
    # Formula: (from_amount * from_price) / to_price
    exchange_rate = from_price / to_price
    converted_amount = amount_float * exchange_rate

    logger.info(
        f"Converted {amount_float} {from_currency} to {converted_amount:.8f} {to_currency} "
        f"(Rate: 1 {from_currency} = {exchange_rate:.8f} {to_currency})"
    )

    return converted_amount, {
        "success": True,
        "from_currency": from_currency,
        "to_currency": to_currency,
        "amount": amount_float,
        "converted_amount": converted_amount,
        "exchange_rate": exchange_rate,
        "from_price_usd": from_price,
        "to_price_usd": to_price,
    }


async def format_payment_with_conversion(
    payment_data: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Process payment data to include currency conversion information when needed.

    This function checks for transactions where auto_convert.processed is False
    and adds the manual conversion information to the payment data.

    Args:
        payment_data: The payment data from OXA Pay API

    Returns:
        dict: The processed payment data with conversion information
    """
    # Make a copy to avoid modifying the original
    result = payment_data.copy()

    target_currency = result.get("currency", "USDT")
    transactions = result.get("txs", [])

    # Track total converted amount from all transactions
    total_converted_amount = 0.0
    conversion_details = []

    for tx_index, tx in enumerate(transactions):
        # Check if auto_convert.processed is False but we still have a transaction
        auto_convert = tx.get("auto_convert", {})

        # Check if this transaction needs manual conversion
        if not auto_convert.get("processed", False) and tx.get("status") in [
            "confirmed",
            "success",
        ]:
            tx_amount = tx.get("amount")
            tx_currency = tx.get("currency")

            if tx_amount and tx_currency and tx_currency != target_currency:
                # Perform the conversion
                converted_amount, conversion_info = await convert_currency(
                    tx_amount, tx_currency, target_currency
                )

                # Add conversion details to the transaction
                tx["manual_convert"] = {
                    "processed": True,
                    "amount": converted_amount,
                    "currency": target_currency,
                    "exchange_rate": conversion_info.get("exchange_rate"),
                    "original_amount": tx_amount,
                    "original_currency": tx_currency,
                }

                # Track total converted amount
                total_converted_amount += converted_amount

                # Store conversion details for reporting
                conversion_details.append(
                    {
                        "tx_index": tx_index,
                        "original": f"{tx_amount} {tx_currency}",
                        "converted": f"{converted_amount:.8f} {target_currency}",
                        "rate": f"1 {tx_currency} = {conversion_info.get('exchange_rate'):.8f} {target_currency}",
                    }
                )

    # If we performed any conversions, add summary to the result
    if conversion_details:
        result["manual_conversion"] = {
            "processed": True,
            "total_converted": total_converted_amount,
            "currency": target_currency,
            "details": conversion_details,
        }

    return result


def get_formatted_conversion_summary(payment_data: Dict[str, Any]) -> str:
    """
    Create a comprehensive human-readable summary of currency conversions in the payment.

    Args:
        payment_data: The payment data with conversion information

    Returns:
        str: Formatted summary of conversions with enhanced visual presentation
    """
    if "manual_conversion" not in payment_data:
        return ""

    conversion = payment_data["manual_conversion"]
    target_currency = conversion.get("currency", "USDT")
    total = conversion.get("total_converted", 0)

    # Start with a clear header section
    lines = [
        f"💱 <b>\u2022 CURRENCY CONVERSION \u2022</b>\n",
        f"<b>━━━━━━━━━━━━━━━━━━</b>",
        f"<b>TRANSACTION DETAILS</b>",
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n",
    ]

    # Add summary line
    total_txs = len(conversion.get("details", []))
    lines.append(f"\u2022 <b>Transactions:</b> {total_txs}")
    lines.append(f"\u2022 <b>Target Currency:</b> {target_currency}")
    lines.append("")

    # Group similar conversions together
    from_currencies = {}
    for detail in conversion.get("details", []):
        # Extract currency from the original string (format: "amount currency")
        parts = detail["original"].split()
        if len(parts) >= 2:
            original_currency = parts[-1]
            if original_currency not in from_currencies:
                from_currencies[original_currency] = []
            from_currencies[original_currency].append(detail)

    # Display conversions grouped by currency
    if from_currencies:
        lines.append("<b>Conversion Breakdown:</b>")

        for currency, details in from_currencies.items():
            # Add currency header
            lines.append(f"\n🔹 <b>{currency} to {target_currency}</b>")

            # Track total for this currency
            currency_total_original = 0
            currency_total_converted = 0

            # Add individual transactions
            for detail in details:
                # Extract amounts for calculations
                original_parts = detail["original"].split()
                converted_parts = detail["converted"].split()

                if len(original_parts) >= 1 and len(converted_parts) >= 1:
                    try:
                        original_amount = float(original_parts[0])
                        converted_amount = float(converted_parts[0])

                        currency_total_original += original_amount
                        currency_total_converted += converted_amount

                        # Format with fixed precision for better readability
                        orig_formatted = (
                            f"{original_amount:.8f}".rstrip("0").rstrip(".")
                            if "." in f"{original_amount:.8f}"
                            else f"{original_amount:.0f}"
                        )
                        conv_formatted = (
                            f"{converted_amount:.8f}".rstrip("0").rstrip(".")
                            if "." in f"{converted_amount:.8f}"
                            else f"{converted_amount:.0f}"
                        )

                        lines.append(
                            f"  • <code>{orig_formatted}</code> → <code>{conv_formatted}</code>"
                        )
                    except ValueError:
                        # Fallback to original format if parsing fails
                        lines.append(
                            f"  • {detail['original']} → {detail['converted']}"
                        )

            # Add subtotal for this currency group
            if len(details) > 1:
                lines.append(
                    f"  <i>Subtotal: {currency_total_original:.8f} {currency} → {currency_total_converted:.8f} {target_currency}</i>"
                )

    # Add exchange rates section
    lines.append("\n<b>Exchange Rates Used:</b>")
    rates_shown = set()

    for detail in conversion.get("details", []):
        if "rate" in detail and detail["rate"] not in rates_shown:
            lines.append(f"  • <i>{detail['rate']}</i>")
            rates_shown.add(detail["rate"])

    # Add grand total with prominent styling
    lines.append("\n<b>━━━━━━━━━━━━━━━━━━</b>")
    lines.append(f"💰 <b>TOTAL AMOUNT: {total:.4f} {target_currency}</b>")
    lines.append("<b>━━━━━━━━━━━━━━━━━━</b>")

    return "\n".join(lines)


async def process_payment_with_conversion(
    payment_data: Dict[str, Any], track_id: str, target_currency: str = "USDT"
) -> Dict[str, Any]:
    """
    Process payment data and convert non-USDT payments to USDT equivalent.

    This function handles both auto-converted payments from OXA Pay and manual
    conversions for payments that weren't auto-converted.

    Args:
        payment_data: The payment data from OXA Pay API
        track_id: Payment tracking ID for logging
        target_currency: Target currency for conversion (default: USDT)

    Returns:
        dict: Processed payment data with conversion information
    """
    result = {
        "original_data": payment_data.copy(),
        "converted_amount": 0.0,
        "conversion_details": [],
        "currency": target_currency,
        "is_converted": False,
        "conversion_errors": [],
        "track_id": track_id,
    }

    transactions = payment_data.get("txs", [])
    if not transactions:
        logger.warning(f"No transactions found in payment data for {track_id}")
        return result

    total_converted_amount = 0.0
    conversion_details = []

    for tx_index, tx in enumerate(transactions):
        tx_currency = tx.get("currency", "USDT").upper()
        tx_amount = 0.0
        converted_amount = 0.0
        conversion_info = {}

        # Check if already auto-converted by OXA Pay
        if "auto_convert_amount" in tx and tx.get("auto_convert_amount") is not None:
            try:
                converted_amount = float(tx.get("auto_convert_amount", 0))

                # Get original amount - check multiple possible field names
                original_amount = 0.0
                amount_fields = ["sent_amount", "amount", "value", "received_amount"]
                for field in amount_fields:
                    if field in tx and tx.get(field) is not None:
                        try:
                            original_amount = float(tx.get(field, 0))
                            if original_amount > 0:
                                break
                        except (ValueError, TypeError):
                            continue

                # Enhanced handling for auto_convert_amount = 0 cases
                if converted_amount == 0:
                    # Case 1: USDT auto-conversions where auto_convert_amount might be 0
                    # but the original transaction is already in USDT
                    if (
                        is_usd_stablecoin(tx_currency)
                        and is_usd_stablecoin(target_currency)
                        and original_amount > 0
                    ):
                        # For USDT to USDT "auto-conversions", use the original amount
                        converted_amount = original_amount
                        logger.info(
                            f"USDT auto-conversion fix for transaction {tx_index} in {track_id}: "
                            f"Using original amount {original_amount} {tx_currency} instead of 0.0"
                        )

                    # Case 2: Non-stablecoin currencies with auto_convert_amount = 0 but value field available
                    # The 'value' field often contains the USD equivalent amount
                    elif (
                        not is_usd_stablecoin(tx_currency)
                        and "value" in tx
                        and tx.get("value") is not None
                    ):
                        try:
                            value_amount = float(tx.get("value", 0))
                            if value_amount > 0:
                                # Use the value field as the USDT equivalent
                                converted_amount = value_amount
                                logger.info(
                                    f"Using 'value' field for auto-conversion in transaction {tx_index} for {track_id}: "
                                    f"{original_amount} {tx_currency} -> {converted_amount} {target_currency} "
                                    f"(auto_convert_amount was 0, using value field)"
                                )
                        except (ValueError, TypeError) as e:
                            logger.warning(
                                f"Failed to parse 'value' field for transaction {tx_index} in {track_id}: {e}"
                            )

                # Determine the specific auto-conversion method used
                auto_convert_method = "auto_converted"
                auto_convert_currency = tx.get("auto_convert_currency", target_currency)

                # Track if we used the value field as fallback
                if (
                    float(tx.get("auto_convert_amount", 0)) == 0
                    and converted_amount > 0
                ):
                    if is_usd_stablecoin(tx_currency):
                        auto_convert_method = "auto_converted_usdt_fix"
                    else:
                        auto_convert_method = "auto_converted_value_field"
                        # When using value field, the target currency is typically USDT/USD equivalent
                        auto_convert_currency = target_currency

                conversion_details.append(
                    {
                        "tx_index": tx_index,
                        "method": auto_convert_method,
                        "original_amount": original_amount,
                        "original_currency": tx_currency,
                        "converted_amount": converted_amount,
                        "converted_currency": auto_convert_currency,
                        "exchange_rate": (
                            converted_amount / original_amount
                            if original_amount > 0
                            else 0
                        ),
                        "tx_hash": tx.get("tx_hash", ""),
                        "auto_convert_amount_original": float(
                            tx.get("auto_convert_amount", 0)
                        ),
                        "value_field_used": float(tx.get("auto_convert_amount", 0)) == 0
                        and "value" in tx,
                    }
                )

                total_converted_amount += converted_amount
                result["is_converted"] = True

                logger.info(
                    f"Auto-converted transaction {tx_index} for {track_id}: "
                    f"{original_amount} {tx_currency} -> {converted_amount} {target_currency}"
                )

            except (ValueError, TypeError) as e:
                logger.error(
                    f"Error processing auto-converted amount for {track_id}: {e}"
                )
                result["conversion_errors"].append(
                    f"Auto-conversion error in tx {tx_index}: {e}"
                )

        else:
            # Manual conversion needed
            try:
                # Get the transaction amount - check multiple possible field names
                # Different API responses may use different field names for the amount
                tx_amount = 0.0
                amount_field_used = None

                # Check for amount fields in order of preference
                amount_fields = ["amount", "value", "received_amount", "sent_amount"]
                for field in amount_fields:
                    if field in tx and tx.get(field) is not None:
                        try:
                            tx_amount = float(tx.get(field, 0))
                            if tx_amount > 0:
                                amount_field_used = field
                                break
                        except (ValueError, TypeError):
                            continue

                if tx_amount <= 0:
                    logger.warning(
                        f"No valid amount found for transaction {tx_index} in {track_id}. Checked fields: {amount_fields}"
                    )
                    continue

                logger.debug(
                    f"Using amount field '{amount_field_used}' = {tx_amount} for transaction {tx_index} in {track_id}"
                )

                # Convert if not already in target currency or equivalent stablecoin
                needs_conversion = tx_currency != target_currency.upper() and not (
                    is_usd_stablecoin(tx_currency)
                    and is_usd_stablecoin(target_currency)
                )

                if needs_conversion:
                    converted_amount, conversion_info = await convert_currency(
                        tx_amount, tx_currency, target_currency
                    )

                    if conversion_info.get("success", False):
                        conversion_details.append(
                            {
                                "tx_index": tx_index,
                                "method": "manual_converted",
                                "original_amount": tx_amount,
                                "original_currency": tx_currency,
                                "converted_amount": converted_amount,
                                "converted_currency": target_currency,
                                "exchange_rate": conversion_info.get(
                                    "exchange_rate", 0
                                ),
                                "tx_hash": tx.get("tx_hash", ""),
                            }
                        )

                        total_converted_amount += converted_amount
                        result["is_converted"] = True

                        logger.info(
                            f"Manual conversion for transaction {tx_index} in {track_id}: "
                            f"{tx_amount} {tx_currency} -> {converted_amount} {target_currency}"
                        )
                    else:
                        error_msg = conversion_info.get(
                            "error", "Unknown conversion error"
                        )
                        logger.error(
                            f"Conversion failed for {track_id} tx {tx_index}: {error_msg}"
                        )
                        result["conversion_errors"].append(
                            f"Conversion error in tx {tx_index}: {error_msg}"
                        )

                        # Use original amount as fallback
                        converted_amount = tx_amount
                        total_converted_amount += converted_amount

                        conversion_details.append(
                            {
                                "tx_index": tx_index,
                                "method": "fallback_original",
                                "original_amount": tx_amount,
                                "original_currency": tx_currency,
                                "converted_amount": converted_amount,
                                "converted_currency": tx_currency,
                                "exchange_rate": 1.0,
                                "tx_hash": tx.get("tx_hash", ""),
                                "error": error_msg,
                            }
                        )
                else:
                    # Already in target currency or equivalent stablecoin
                    converted_amount = tx_amount
                    total_converted_amount += converted_amount

                    # Determine the method based on whether it's same currency or stablecoin equivalence
                    if tx_currency == target_currency.upper():
                        method = "no_conversion_needed"
                        log_msg = f"No conversion needed: {tx_amount} {tx_currency} (already in target currency)"
                    else:
                        method = "stablecoin_equivalent"
                        log_msg = f"Stablecoin equivalence: {tx_amount} {tx_currency} = {converted_amount} {target_currency} (USD-pegged stablecoins)"

                    logger.info(f"Transaction {tx_index} for {track_id}: {log_msg}")

                    conversion_details.append(
                        {
                            "tx_index": tx_index,
                            "method": method,
                            "original_amount": tx_amount,
                            "original_currency": tx_currency,
                            "converted_amount": converted_amount,
                            "converted_currency": target_currency,
                            "exchange_rate": 1.0,
                            "tx_hash": tx.get("tx_hash", ""),
                        }
                    )

            except (ValueError, TypeError) as e:
                logger.error(
                    f"Error processing transaction {tx_index} for {track_id}: {e}"
                )
                result["conversion_errors"].append(
                    f"Processing error in tx {tx_index}: {e}"
                )

    result["converted_amount"] = total_converted_amount
    result["conversion_details"] = conversion_details

    logger.info(
        f"Payment conversion completed for {track_id}: "
        f"Total converted amount: {total_converted_amount} {target_currency}, "
        f"Transactions processed: {len(conversion_details)}, "
        f"Errors: {len(result['conversion_errors'])}"
    )

    return result


def format_conversion_display(
    conversion_result: Dict[str, Any], fee_rate: float = 0.0
) -> str:
    """
    Format conversion details for user display with enhanced visual presentation.

    Args:
        conversion_result: Result from process_payment_with_conversion
        fee_rate: Payment fee rate to add back to the total (e.g., 0.015 for 1.5%)

    Returns:
        str: Formatted conversion display text with improved typography and styling
    """
    if not conversion_result.get("is_converted", False):
        return ""

    details = conversion_result.get("conversion_details", [])
    if not details:
        return ""

    # Create user-friendly header section
    lines = [
        f"💱 <b>Payment Processing Details</b>\n",
        f"<b>━━━━━━━━━━━━━━━━━━</b>",
        f"<b>TRANSACTION SUMMARY</b>",
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n",
    ]

    # Track subtotals by method for summary
    method_subtotals = {
        "auto": 0.0,
        "manual": 0.0,
        "stable": 0.0,
        "original": 0.0,
        "fallback": 0.0,
    }

    # Group by conversion method
    auto_converted = [
        d
        for d in details
        if d["method"]
        in ["auto_converted", "auto_converted_usdt_fix", "auto_converted_value_field"]
    ]
    manual_converted = [d for d in details if d["method"] == "manual_converted"]
    no_conversion = [d for d in details if d["method"] == "no_conversion_needed"]
    stablecoin_equivalent = [
        d for d in details if d["method"] == "stablecoin_equivalent"
    ]
    fallback = [d for d in details if d["method"] == "fallback_original"]

    currency = conversion_result.get("currency", "USDT")

    # Add overview stats
    tx_count = len(details)
    lines.append(f"\u2022 <b>Transactions:</b> {tx_count}")
    lines.append(f"\u2022 <b>Target Currency:</b> {currency}")
    has_errors = len(fallback) > 0
    if has_errors:
        lines.append(f"\u2022 <b>Status:</b> ⚠️ Completed with {len(fallback)} issues")
    else:
        lines.append(f"\u2022 <b>Status:</b> ✅ Successfully processed")
    lines.append("")

    # Process auto-converted transactions
    if auto_converted:
        lines.append("🏦 <b>Automatic Processing</b>")
        lines.append("<i>✨ Converted automatically by payment gateway</i>")

        auto_total = 0
        for detail in auto_converted:
            # Format amounts with better readability
            original_amt = f"{detail['original_amount']:.8f}".rstrip("0").rstrip(".")
            converted_amt = f"{detail['converted_amount']:.4f}"
            auto_total += detail["converted_amount"]

            # Add method-specific indicators
            method_indicator = ""
            if detail["method"] == "auto_converted_value_field":
                method_indicator = " 📊"  # Value field used
            elif detail["method"] == "auto_converted_usdt_fix":
                method_indicator = " 🔧"  # USDT fix applied

            lines.append(
                f"  \u2022 <code>{original_amt}</code> <b>{detail['original_currency']}</b> "
                f"<b>→</b> <code>{converted_amt}</code> <b>{detail['converted_currency']}</b>{method_indicator}"
            )

        method_subtotals["auto"] = auto_total
        if len(auto_converted) > 1:
            lines.append(f"  <i>Subtotal: {auto_total:.4f} {currency}</i>")
        lines.append("")

    # Process manual converted transactions
    if manual_converted:
        lines.append("🔄 <b>Currency Exchange</b>")
        lines.append("<i>📊 Converted using current market rates</i>")

        manual_total = 0
        for detail in manual_converted:
            # Format amounts with better readability
            original_amt = f"{detail['original_amount']:.8f}".rstrip("0").rstrip(".")
            converted_amt = f"{detail['converted_amount']:.4f}"
            manual_total += detail["converted_amount"]

            # Add rate info
            rate = detail.get("exchange_rate", 0)
            rate_display = f" <i>(Rate: {rate:.6f})</i>" if rate > 0 else ""

            lines.append(
                f"  \u2022 <code>{original_amt}</code> <b>{detail['original_currency']}</b> "
                f"<b>→</b> <code>{converted_amt}</code> <b>{detail['converted_currency']}</b>{rate_display}"
            )

        method_subtotals["manual"] = manual_total
        if len(manual_converted) > 1:
            lines.append(f"  <i>Subtotal: {manual_total:.4f} {currency}</i>")
        lines.append("")

    # Process stablecoin equivalence transactions
    if stablecoin_equivalent:
        lines.append("🪙 <b>Stablecoin Processing</b>")
        lines.append("<i>🔗 Direct 1:1 USD equivalent conversion</i>")

        stable_total = 0
        for detail in stablecoin_equivalent:
            # Format amounts with better precision for stablecoins
            amount = f"{detail['original_amount']:.2f}"
            stable_total += detail["converted_amount"]

            lines.append(
                f"  \u2022 <code>{amount}</code> <b>{detail['original_currency']}</b> "
                f"<b>=</b> <code>{amount}</code> <b>{detail['converted_currency']}</b>"
            )

        method_subtotals["stable"] = stable_total
        if len(stablecoin_equivalent) > 1:
            lines.append(f"  <i>Subtotal: {stable_total:.2f} {currency}</i>")
        lines.append("")

    # Process no conversion needed transactions
    if no_conversion:
        lines.append("✓ <b>Direct Processing</b>")
        lines.append("<i>💼 No conversion needed - already in USDT</i>")

        original_total = 0
        for detail in no_conversion:
            amount = f"{detail['original_amount']:.4f}"
            original_total += detail["converted_amount"]

            lines.append(
                f"  \u2022 <code>{amount}</code> <b>{detail['original_currency']}</b>"
            )

        method_subtotals["original"] = original_total
        if len(no_conversion) > 1:
            lines.append(f"  <i>Subtotal: {original_total:.4f} {currency}</i>")
        lines.append("")

    # Process fallback/error transactions
    if fallback:
        lines.append("⚠️ <b>Processing Notes</b>")
        lines.append("<i>🚨 Some amounts processed at original values</i>")

        fallback_total = 0
        for detail in fallback:
            error_msg = detail.get("error", "Unknown issue")
            # Truncate very long error messages
            if len(error_msg) > 30:
                error_msg = error_msg[:27] + "..."

            fallback_total += detail["converted_amount"]

            lines.append(
                f"  \u2022 <code>{detail['original_amount']:.8f}</code> <b>{detail['original_currency']}</b> "
                f"<i>({error_msg})</i>"
            )

        method_subtotals["fallback"] = fallback_total
        if len(fallback) > 1:
            lines.append(
                f"  <i>Subtotal: {fallback_total:.4f} {detail['converted_currency']}</i>"
            )
        lines.append("")

    # Enhanced total section with prominent styling
    total_amount = conversion_result.get("converted_amount", 0)

    # Apply fee adjustment if fee_rate is provided and total_amount > 0
    fee_amount = 0
    if fee_rate > 0 and total_amount > 0:
        fee_amount = total_amount * fee_rate / (1 - fee_rate)
        final_amount = total_amount + fee_amount
        logger.info(
            f"Applied fee adjustment in display: "
            f"{total_amount:.2f} + {fee_amount:.2f} fee = {final_amount:.2f} {currency} "
            f"(adding back {fee_rate*100:.1f}% fee)"
        )
        total_amount = final_amount

    if lines:
        # Remove the last empty line if it exists
        if lines and lines[-1] == "":
            lines.pop()

        # Add breakdown table with improved labels
        if sum(1 for v in method_subtotals.values() if v > 0) > 1:
            lines.append("\n<b>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</b>")
            lines.append("<b>📊 Processing Summary</b>")

            if method_subtotals["auto"] > 0:
                lines.append(
                    f"  💳 Automatic Processing: <code>{method_subtotals['auto']:.2f}</code> {currency}"
                )
            if method_subtotals["manual"] > 0:
                lines.append(
                    f"  🔄 Currency Exchange: <code>{method_subtotals['manual']:.2f}</code> {currency}"
                )
            if method_subtotals["stable"] > 0:
                lines.append(
                    f"  🪙 Stablecoin Processing: <code>{method_subtotals['stable']:.2f}</code> {currency}"
                )
            if method_subtotals["original"] > 0:
                lines.append(
                    f"  ✓ Direct Processing: <code>{method_subtotals['original']:.2f}</code> {currency}"
                )
            if method_subtotals["fallback"] > 0:
                lines.append(
                    f"  ⚠️ Special Processing: <code>{method_subtotals['fallback']:.2f}</code> {currency}"
                )

        # Fee display if applicable
        if fee_rate > 0:
            base_amount = total_amount - fee_amount
            lines.append("\n<b>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</b>")
            lines.append("<b>💰 Fee Information</b>")
            lines.append(
                f"  💵 Base Amount: <code>{base_amount:.2f}</code> {currency}"
            )
            lines.append(
                f"  📊 Processing Fee ({fee_rate*100:.1f}%): <code>+{fee_amount:.2f}</code> {currency}"
            )

        # Final total with enhanced styling
        lines.append("\n<b>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</b>")
        lines.append(f"🎯 <b>Total Processed Amount</b>")
        lines.append(f"💎 <code><b>{total_amount:.2f}</b></code> <b>{currency}</b>")
        lines.append("<b>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</b>")

    return "\n".join(lines)


async def get_usdt_equivalent_amount(
    amount: Union[float, str], currency: str, track_id: str = "unknown"
) -> Tuple[float, bool, str]:
    """
    Get USDT equivalent of an amount in any currency.

    Args:
        amount: Amount to convert
        currency: Source currency
        track_id: Payment tracking ID for logging

    Returns:
        tuple: (usdt_amount, success, error_message)
    """
    try:
        amount_float = float(amount)
    except (ValueError, TypeError):
        return 0.0, False, f"Invalid amount format: {amount}"

    # Check if currency is already a USD stablecoin (no conversion needed)
    if is_usd_stablecoin(currency):
        logger.info(
            f"No conversion needed for USD stablecoin: {amount_float} {currency.upper()} = {amount_float} USDT"
        )
        return amount_float, True, ""

    converted_amount, conversion_info = await convert_currency(
        amount_float, currency, "USDT"
    )

    if conversion_info.get("success", False):
        logger.info(
            f"Converted {amount_float} {currency} to {converted_amount} USDT for {track_id}"
        )
        return converted_amount, True, ""
    else:
        error_msg = conversion_info.get("error", "Unknown conversion error")
        logger.error(
            f"Failed to convert {amount_float} {currency} to USDT for {track_id}: {error_msg}"
        )
        return 0.0, False, error_msg
