import aiohttp
import json
import sys
import time
import asyncio
import os
import logging
from typing import Dict, Any, Optional, Union, List
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

# Update imports to use OXA_PAY_CALLBACK_URL from config
from payments.payment_config import (
    OXA_PAY_API_KEY,
    get_callback_url,
    get_external_callback_url,
)
from config import DEVELOPMENT_MODE, OXA_PAY_CALLBACK_URL


def is_development_mode():
    """Check if we're in development mode and return 'true' or 'false' as a lowercase string"""
    return os.environ.get("DEVELOPMENT_MODE", "false").lower() in ("true", "1", "yes")


# Set up proper logging
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    handler.setFormatter(
        logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    )
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# Log callback URL once at module import time
logger.info(f"Using callback URL from config: {OXA_PAY_CALLBACK_URL}")


def append_params_to_url(url: str, params: Dict[str, Any]) -> str:
    """Safely append parameters to a URL regardless of existing query parameters"""
    if not url:
        return url

    parsed_url = urlparse(url)
    query_dict = parse_qs(parsed_url.query)

    # Merge existing parameters with new ones
    for key, value in params.items():
        if value is not None:
            query_dict[key] = [str(value)]

    # Rebuild the query string
    new_query = urlencode(query_dict, doseq=True)

    # Replace the query component in the parsed URL
    new_url_parts = list(parsed_url)
    new_url_parts[4] = new_query

    # Return the reconstructed URL
    return urlunparse(new_url_parts)


async def create_payment_link(
    amount: float = 100,
    order_id: Optional[str] = None,
    description: Optional[str] = None,
    generate_button: bool = False,
    button_text: str = "Pay Now",
    button_class: str = "pay-button",
    callback_url: Optional[str] = None,
    return_url: Optional[str] = None,
    user_id: Optional[str] = None,
    currency: str = "USDT",
    lifetime: int = 60,
    fee_paid_by_payer: int = 1,
    under_paid_coverage: float = 15,
    to_currency: Optional[str] = "USDT",
    auto_withdrawal: int = 0,
    mixed_payment: int = 0,
    thanks_message: Optional[str] = None,
    sandbox: Optional[bool] = None,
) -> Dict[str, Any]:
    """
    Create a payment link for the OXA Pay payment gateway.

    If sandbox=None (default), the sandbox mode will be automatically set based on
    the DEVELOPMENT_MODE environment variable:
    - When DEVELOPMENT_MODE=true: sandbox=True (test mode)
    - When DEVELOPMENT_MODE=false: sandbox=False (production mode)

    You can override this behavior by explicitly setting sandbox=True or sandbox=False.
    """
    # Set sandbox mode based on development mode if not explicitly provided
    if sandbox is None:
        dev_mode = is_development_mode()
        sandbox = True if dev_mode else False
        logger.info(
            f"Setting sandbox mode to {sandbox} based on DEVELOPMENT_MODE={dev_mode}"
        )

    # Set default values
    if order_id is None:
        order_id = f"ORD-{int(time.time())}"

    if description is None:
        description = f"Order {order_id}"

    # Process callback URL with enhanced fallback mechanism
    if callback_url:
        # If callback URL is explicitly provided, use it regardless of mode
        final_callback_url = callback_url
        logger.info(f"Using explicitly provided callback URL: {final_callback_url}")
    else:
        # Check if OXA_PAY_CALLBACK_URL is provided and not empty
        if OXA_PAY_CALLBACK_URL and OXA_PAY_CALLBACK_URL.strip():
            # Use the configured callback URL from environment
            final_callback_url = OXA_PAY_CALLBACK_URL
            dev_mode = is_development_mode()
            mode_text = "development" if dev_mode else "production"
            logger.info(
                f"Using OXA_PAY_CALLBACK_URL from environment in {mode_text} mode: {final_callback_url}"
            )
        else:
            # Fallback to dynamic URL generation when OXA_PAY_CALLBACK_URL is empty/missing
            # Always use external callback URL (real server IP) for payment providers
            final_callback_url = get_external_callback_url()
            dev_mode = is_development_mode()
            mode_text = "development" if dev_mode else "production"
            logger.info(
                f"OXA_PAY_CALLBACK_URL not configured - using dynamic callback URL with server IP in {mode_text} mode: {final_callback_url}"
            )

    # Add user_id as query parameter if provided
    if user_id:
        final_callback_url = append_params_to_url(
            final_callback_url, {"user_id": user_id}
        )

    # New API endpoint for v1
    url = "https://api.oxapay.com/v1/payment/invoice"

    # Construct payload according to new API specifications
    data = {
        "amount": amount,
        "currency": currency,
        "lifetime": lifetime,
        "fee_paid_by_payer": fee_paid_by_payer,
        "under_paid_coverage": under_paid_coverage,
        "callback_url": final_callback_url,
        "description": description,
        "order_id": order_id,
        "sandbox": True if sandbox else False,
    }

    # Add optional parameters only if they're specified
    if to_currency:
        data["to_currency"] = to_currency

    if auto_withdrawal is not None:
        data["auto_withdrawal"] = 1 if auto_withdrawal else 0

    if mixed_payment is not None:
        data["mixed_payment"] = 1 if mixed_payment else 0

    if thanks_message:
        data["thanks_message"] = thanks_message

    if user_id:
        data["email"] = (
            f"user_{user_id}@example.com"  # Optional placeholder for user reference
        )

    # Log the payment request data
    logger.info(f"Creating payment link with data: {json.dumps(data, indent=2)}")

    try:
        # Use aiohttp instead of requests for non-blocking HTTP requests
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=data,
                headers={
                    "Content-Type": "application/json",
                    "merchant_api_key": OXA_PAY_API_KEY,
                },
                timeout=10,
            ) as response:
                if response.status == 200:
                    result = await response.json()

                    # Format response according to new API structure
                    if result.get("status") == 200:
                        response_data = result.get("data", {})
                        track_id = response_data.get("track_id")
                        payment_url = response_data.get("payment_url")

                        formatted_response = {
                            "status": "success",
                            "trackId": track_id,
                            "payLink": payment_url,
                            "orderId": order_id,
                            "amount": amount,
                            "expiration": response_data.get("expired_at"),
                            "message": result.get("message"),
                            "raw_response": result,
                            "callback_url": final_callback_url,
                        }

                        # Generate button HTML if requested
                        if generate_button and payment_url:
                            button_html = f'<button class="{button_class}" onclick="window.location.href=\'{payment_url}\'">{button_text}</button>'
                            formatted_response["button_html"] = button_html

                        return formatted_response
                    else:
                        error_data = result.get("error", {})
                        return {
                            "status": "error",
                            "message": error_data.get("message")
                            or result.get("message", "Unknown error"),
                            "error_type": error_data.get("type"),
                            "error_key": error_data.get("key"),
                            "raw_response": result,
                        }
                else:
                    error_text = await response.text()
                    return {
                        "status": "error",
                        "message": f"HTTP Error {response.status}: {error_text}",
                        "raw_response": None,
                    }
    except aiohttp.ClientError as e:
        logger.error(f"Connection error: {e}")
        return {
            "status": "error",
            "message": f"Connection error: {str(e)}",
            "raw_response": None,
        }
    except asyncio.TimeoutError:
        logger.error("Request timed out")
        return {"status": "error", "message": "Request timed out", "raw_response": None}
    except Exception as e:
        logger.exception(f"Unexpected error: {e}")
        return {"status": "error", "message": str(e), "raw_response": None}


def create_payment_link_sync(**kwargs) -> Dict[str, Any]:
    """
    Synchronous wrapper around the async payment link function.

    Takes the same parameters as create_payment_link.
    """
    return asyncio.run(create_payment_link(**kwargs))


# Allow running the script directly with command-line arguments
if __name__ == "__main__":
    # Get amount from command line arguments if provided
    amount = 100
    order_id = None

    if len(sys.argv) >= 2:
        try:
            amount = float(sys.argv[1])
        except ValueError:
            print(json.dumps({"status": "error", "message": "Invalid amount"}))
            sys.exit(1)

    if len(sys.argv) >= 3:
        order_id = sys.argv[2]

    # Create the payment link
    result = create_payment_link_sync(amount=amount, order_id=order_id)

    # Print the result as JSON for easy parsing by other scripts
    print(json.dumps(result))
