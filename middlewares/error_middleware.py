from typing import Dict, Any, Callable, Awaitable, Optional
import logging
import traceback
from datetime import datetime
import json
import os
from aiogram import BaseMiddleware
from aiogram.types import TelegramObject, User, Message, CallbackQuery, Update
from aiogram.exceptions import TelegramBadRequest
from config import OWNER_ID, ADMIN_ID

logger = logging.getLogger(__name__)

# Import template helper
try:
    from utils.template_helpers import format_text
except ImportError:
    logger = logging.getLogger(__name__)
    logger.error("Failed to import format_text from utils.template_helpers")

    # Define a fallback format_text function
    def format_text(template_type, template_name, **kwargs):
        # Simple fallback that just returns the default or a basic message
        default = kwargs.get("default", f"Error {kwargs.get('error_id', 'unknown')}")
        return default


# Define the path for error logs at the project root
ERROR_LOG_PATH = os.path.join(
    os.path.dirname(os.path.dirname(__file__)), "error_logs"
)  # Changed path
os.makedirs(ERROR_LOG_PATH, exist_ok=True)


class ErrorHandlingMiddleware(BaseMiddleware):
    """
    Middleware for handling errors that occur during update processing.
    Provides detailed error logging, notification, and recovery mechanisms.
    """

    async def __call__(
        self,
        handler: Callable[[Update, Dict[str, Any]], Awaitable[Any]],
        event: Update,
        data: Dict[str, Any],
    ) -> Any:
        """Execute middleware."""
        bot = data.get("bot")

        try:
            # Pass control to the next handler
            return await handler(event, data)
        except Exception as e:
            # Generate unique error ID
            error_id = (
                f"ERR-{datetime.now().strftime('%Y%m%d%H%M%S')}-{hash(str(e)) % 10000}"
            )

            # Determine user info
            user_id = "Unknown"
            if isinstance(event, Message) and event.from_user:
                user_id = event.from_user.id
            elif isinstance(event, CallbackQuery) and event.from_user:
                user_id = event.from_user.id

            # Log the error with traceback
            logger.error(
                f"Error ID: {error_id} | User: {user_id} | Error: {str(e)}",
                exc_info=True,
            )

            # Optionally notify user (only for user-visible errors)
            try:
                if isinstance(event, Message) and bot:
                    await event.answer(
                        f"⚠️ An error occurred (ID: {error_id}).\n"
                        f"Our team has been notified.",
                        parse_mode="HTML",
                    )
                elif isinstance(event, CallbackQuery) and bot:
                    await event.answer(f"Error: {error_id}", show_alert=True)
            except Exception as notify_err:
                logger.error(f"Failed to notify user about error: {notify_err}")

            await self._notify_user(event, error_id)

            # Try to notify the owner/admin about critical errors
            if self._is_critical_error(e):
                await self._notify_admin(event, e, error_id, data.get("bot"))

            # Return None or False to prevent further processing
            return None

    async def on_process_error(self, update: Update, exception: Exception, data: dict):
        """
        Handle errors that occur during message processing.
        Create error logs and notify admins for critical errors.
        """
        # Skip common non-critical errors (not actual errors, just notice-level issues)
        if isinstance(exception, TelegramBadRequest):
            error_message = str(exception).lower()
            if "message is not modified" in error_message:
                # This is not a real error, just log at debug level and return
                logger.debug(
                    "Ignoring 'message is not modified' error - not an actual error"
                )
                return
            if "message to edit not found" in error_message:
                # This is a common issue when users delete messages
                logger.info("Message to edit was not found - likely deleted by user")
                return

        # For real errors, continue with normal error handling
        # Generate a unique error ID with timestamp
        error_id = self._generate_error_id()

        # Log the error with complete traceback
        logger.error(
            f"Error {error_id}: {exception}",
            exc_info=True,
        )

    async def _log_error(
        self, e: Exception, event: TelegramObject, data: Dict[str, Any]
    ) -> str:
        """
        Log detailed error information to file and database.
        Returns a unique error ID for reference.
        """
        # Create unique error ID
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        # Use modulo to keep the hash part short and consistent
        error_hash = abs(hash(str(e) + timestamp)) % 10000
        error_id = f"ERR-{timestamp}-{error_hash:04d}"

        # Extract user information if available
        user = self._extract_user(event)

        # Format traceback for logging
        tb = traceback.format_exc()

        # Get event type and details
        event_type = event.__class__.__name__
        event_details = self._get_event_details(event)

        # Create error log entry
        error_log = {
            "error_id": error_id,
            "timestamp": datetime.now().isoformat(),
            "error_type": e.__class__.__name__,
            "error_message": str(e),
            "traceback": tb,
            "event_type": event_type,
            "event_details": event_details,
            "user_id": user.id if user else None,
            "username": user.username if user else None,
        }

        # Save error log to file
        error_file = os.path.join(ERROR_LOG_PATH, f"{error_id}.json")
        try:
            with open(error_file, "w", encoding="utf-8") as f:
                json.dump(error_log, f, ensure_ascii=False, indent=2)
        except Exception as file_error:
            logger.error(f"Failed to write error log file {error_file}: {file_error}")

        # Try to log to database if available
        try:
            if "db" in data:
                db = data["db"]
                # Assuming there's an errors collection
                await db.errors.insert_one(error_log)
        except Exception as db_error:
            logger.error(f"Failed to log error to database: {db_error}")

        return error_id

    def _extract_user(self, event: TelegramObject) -> Optional[User]:
        """Extract user information from the event if available."""
        if isinstance(event, Message):
            return event.from_user
        elif isinstance(event, CallbackQuery):
            return event.from_user
        # Add more event types as needed

        return None

    def _get_event_details(self, event: TelegramObject) -> Dict[str, Any]:
        """Extract relevant details from the event for error reporting."""
        try:
            if isinstance(event, Message):
                return {
                    "message_id": event.message_id,
                    "chat_id": event.chat.id,
                    "text": event.text,
                    "content_type": self._get_content_type(event),
                }
            elif isinstance(event, CallbackQuery):
                return {
                    "query_id": event.id,
                    "chat_id": event.message.chat.id if event.message else None,
                    "data": event.data,
                }
            # Add more event types as needed

            # For unknown event types, try to convert to dict
            return {"raw_event": str(event)}
        except Exception as detail_error:
            logger.error(f"Failed to extract event details: {detail_error}")
            return {"error": "Failed to extract event details"}

    def _get_content_type(self, message: Message) -> str:
        """Determine the content type of a message."""
        if message.text:
            return "text"
        elif message.photo:
            return "photo"
        elif message.document:
            return "document"
        elif message.sticker:
            return "sticker"
        # Add more content types as needed
        return "unknown"

    def _is_critical_error(self, e: Exception) -> bool:
        """Determine if an error is critical and should be reported to admins."""
        # Skip reporting common non-critical errors
        error_str = str(e).lower()

        # Common non-critical errors that should not trigger admin notifications
        non_critical_patterns = [
            "message is not modified",  # Common when trying to edit with identical content
            "specified new message content and reply markup are exactly the same",  # Another variant of "message is not modified"
            "message is not modified: specified new message content and reply markup are exactly the same",  # Combined form of the above two errors
            "message to edit not found",  # Message may have been deleted already
            "message to be replied not found",  # Message may have been deleted already
            "query is too old",  # Callback query timeout
            "bot was blocked by the user",  # User blocked the bot
            "chat not found",  # Chat doesn't exist or bot was removed
            "user is deactivated",  # User account is deactivated
            "message can't be deleted",  # Message deletion failed (too old)
        ]

        # Check if the error matches any non-critical pattern
        if any(pattern in error_str for pattern in non_critical_patterns):
            logger.info(
                f"Non-critical error detected, not notifying admin: {error_str}"
            )
            return False

        # List of critical error types
        critical_error_types = (
            ConnectionError,
            TimeoutError,
            MemoryError,
            KeyError,  # Often indicates unexpected data structure
            ValueError,  # Can indicate invalid data processing
            AttributeError,  # Can indicate unexpected object state
            TypeError,  # Can indicate incorrect function arguments
            ImportError,  # Critical if a required module is missing
            # Add other specific exceptions relevant to your bot (e.g., database errors)
        )

        # Check if the exception is a critical type
        is_critical_type = isinstance(e, critical_error_types)

        # Check for critical error messages (use sparingly, type checking is better)
        critical_keywords = [
            "database",
            "connection",
            "timeout",
            "memory",
            "critical",
            "fatal",
            "authentication",  # Example: Payment gateway auth failure
        ]
        # Ensure 'e' is converted to string and lowercased for reliable checking
        has_critical_keywords = any(kw in error_str for kw in critical_keywords)

        return is_critical_type or has_critical_keywords

    async def _notify_user(self, event: TelegramObject, error_id: str) -> None:
        """Notify the user about the error using a template."""
        try:
            message = None

            if isinstance(event, Message):
                message = event
            elif isinstance(event, CallbackQuery):
                message = event.message

            if message:
                # Use template for user notification
                notification_text = format_text(
                    "user", "error_user_notification", error_id=error_id
                )
                await message.reply(
                    notification_text,
                    parse_mode="HTML",
                )
            elif isinstance(event, CallbackQuery):
                # Fallback for callback queries without a message context
                await event.answer(
                    "An error occurred. Ref: " + error_id, show_alert=True
                )
        except Exception as notify_error:
            logger.error(
                f"Failed to notify user about error {error_id}: {notify_error}"
            )

    async def _notify_admin(
        self, event: TelegramObject, e: Exception, error_id: str, bot=None
    ) -> None:
        """Notify admins/owner about critical errors using a template."""
        if not bot:
            logger.warning(
                "Bot instance not available in middleware data for admin notification."
            )
            return

        try:
            # Extract user information
            user = self._extract_user(event)
            user_id_str = str(user.id) if user else "Unknown"
            username_str = (
                f"@{user.username}" if user and user.username else "No username"
            )

            # Skip notification if the user is privileged
            if user and user.id:
                try:
                    # Import here to avoid circular imports
                    from handlers.sys_db import is_privileged

                    if is_privileged(user.id):
                        logger.debug(
                            f"Skipping admin error notification for privileged user (ID: {user.id})"
                        )
                        return
                except ImportError:
                    logger.error("Could not import is_privileged function")
                    # Continue with notification if we can't check privileges

            # Get event details and ensure they're not too long
            event_details = self._get_event_details(event)
            event_details_str = json.dumps(
                event_details, indent=2, ensure_ascii=False
            )  # Pretty print JSON

            # Truncate event details if too long (max ~1000 chars for readability)
            if len(event_details_str) > 1000:
                event_details_str = event_details_str[:997] + "..."

            # Truncate error message if too long (max ~500 chars)
            error_message_str = str(e)
            if len(error_message_str) > 500:
                error_message_str = error_message_str[:497] + "..."

            # Use template for admin notification
            notification = format_text(
                "admin",
                "error_admin_notification",
                error_id=error_id,
                error_type=e.__class__.__name__,
                error_message=error_message_str,
                user_id=user_id_str,
                username=username_str,
                event_details=event_details_str,
            )

            # Final safety check - ensure the entire message is under Telegram's limit
            if len(notification) > 4000:  # Leave some buffer below the 4096 limit
                # If too long, prioritize showing the error ID and type
                notification = (
                    f"🚨 <b>CRITICAL ERROR (Truncated)</b> 🚨\n\n"
                    f"<b>Error ID:</b> <code>{error_id}</code>\n"
                    f"<b>Type:</b> <code>{e.__class__.__name__}</code>\n\n"
                    f"<i>Message too long. Check error log file {error_id}.json for full details.</i>"
                )

            # Send to owner
            await bot.send_message(
                chat_id=OWNER_ID, text=notification, parse_mode="HTML"
            )

            # Send to admin if different from owner
            if ADMIN_ID and str(ADMIN_ID) != str(
                OWNER_ID
            ):  # Ensure comparison works even if one is int and other str
                await bot.send_message(
                    chat_id=ADMIN_ID, text=notification, parse_mode="HTML"
                )
        except Exception as notify_error:
            logger.error(
                f"Failed to notify admin about error {error_id}: {notify_error}"
            )
